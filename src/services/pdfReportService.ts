// PDF Report Generation Service
// This service handles generating various types of PDF reports for the patient management system

interface PatientReportData {
  patient: {
    firstName: string;
    lastName: string;
    patientId: string;
    dateOfBirth: string;
    gender: string;
    phone: string;
    email?: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
    bloodType?: string;
  };
  visits?: Array<{
    visitId: string;
    visitDate: string;
    visitType: string;
    department: string;
    doctor: string;
    chiefComplaint: string;
    diagnosis?: string;
    treatment?: string;
  }>;
  treatmentPlans?: Array<{
    planId: string;
    planName: string;
    startDate: string;
    status: string;
    goals: Array<{
      description: string;
      status: string;
      progress: number;
    }>;
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      status: string;
    }>;
  }>;
  bills?: Array<{
    billId: string;
    billDate: string;
    totalAmount: number;
    paidAmount: number;
    outstandingAmount: number;
    status: string;
    items: Array<{
      description: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
    }>;
  }>;
  vitalSigns?: Array<{
    date: string;
    temperature: number;
    bloodPressure: { systolic: number; diastolic: number };
    heartRate: number;
    weight: number;
    height: number;
  }>;
}

interface ReportOptions {
  reportType: 'patient-summary' | 'medical-history' | 'treatment-plan' | 'billing-statement' | 'visit-summary';
  includeCharts?: boolean;
  dateRange?: { start: string; end: string };
  customFields?: Array<{ label: string; value: string }>;
  hospitalInfo?: {
    name: string;
    address: string;
    phone: string;
    email: string;
    logo?: string;
  };
}

class PDFReportService {
  private hospitalInfo = {
    name: 'Vaidya Hospital',
    tagline: 'Intelligent Care, Ancient Wisdom',
    address: '123 Medical Center Drive, Healthcare City, HC 12345',
    phone: '(*************',
    email: '<EMAIL>'
  };

  // Generate Patient Summary Report
  async generatePatientSummaryReport(data: PatientReportData, options: ReportOptions): Promise<Blob> {
    const htmlContent = this.generatePatientSummaryHTML(data, options);
    return this.convertHTMLToPDF(htmlContent);
  }

  // Generate Medical History Report
  async generateMedicalHistoryReport(data: PatientReportData, options: ReportOptions): Promise<Blob> {
    const htmlContent = this.generateMedicalHistoryHTML(data, options);
    return this.convertHTMLToPDF(htmlContent);
  }

  // Generate Treatment Plan Report
  async generateTreatmentPlanReport(data: PatientReportData, options: ReportOptions): Promise<Blob> {
    const htmlContent = this.generateTreatmentPlanHTML(data, options);
    return this.convertHTMLToPDF(htmlContent);
  }

  // Generate Billing Statement
  async generateBillingStatement(data: PatientReportData, options: ReportOptions): Promise<Blob> {
    const htmlContent = this.generateBillingHTML(data, options);
    return this.convertHTMLToPDF(htmlContent);
  }

  // Generate Visit Summary Report
  async generateVisitSummaryReport(data: PatientReportData, options: ReportOptions): Promise<Blob> {
    const htmlContent = this.generateVisitSummaryHTML(data, options);
    return this.convertHTMLToPDF(htmlContent);
  }

  private generatePatientSummaryHTML(data: PatientReportData, options: ReportOptions): string {
    const currentDate = new Date().toLocaleDateString();
    const age = this.calculateAge(data.patient.dateOfBirth);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Patient Summary Report</title>
        <style>
          ${this.getCommonStyles()}
          .summary-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
          .info-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
          .vital-signs { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }
          .vital-item { text-align: center; padding: 10px; background: #e9ecef; border-radius: 6px; }
        </style>
      </head>
      <body>
        ${this.generateHeader('Patient Summary Report', currentDate)}
        
        <div class="summary-grid">
          <div class="info-card">
            <h3>Patient Information</h3>
            <p><strong>Name:</strong> ${data.patient.firstName} ${data.patient.lastName}</p>
            <p><strong>Patient ID:</strong> ${data.patient.patientId}</p>
            <p><strong>Date of Birth:</strong> ${new Date(data.patient.dateOfBirth).toLocaleDateString()} (Age: ${age})</p>
            <p><strong>Gender:</strong> ${data.patient.gender}</p>
            <p><strong>Blood Type:</strong> ${data.patient.bloodType || 'Not specified'}</p>
            <p><strong>Phone:</strong> ${data.patient.phone}</p>
            <p><strong>Email:</strong> ${data.patient.email || 'Not provided'}</p>
          </div>
          
          <div class="info-card">
            <h3>Address</h3>
            <p>${data.patient.address.street}</p>
            <p>${data.patient.address.city}, ${data.patient.address.state} ${data.patient.address.zipCode}</p>
          </div>
        </div>

        ${data.vitalSigns && data.vitalSigns.length > 0 ? `
          <div class="section">
            <h3>Latest Vital Signs</h3>
            <div class="vital-signs">
              <div class="vital-item">
                <div class="vital-value">${data.vitalSigns[0].temperature}°F</div>
                <div class="vital-label">Temperature</div>
              </div>
              <div class="vital-item">
                <div class="vital-value">${data.vitalSigns[0].bloodPressure.systolic}/${data.vitalSigns[0].bloodPressure.diastolic}</div>
                <div class="vital-label">Blood Pressure</div>
              </div>
              <div class="vital-item">
                <div class="vital-value">${data.vitalSigns[0].heartRate} bpm</div>
                <div class="vital-label">Heart Rate</div>
              </div>
              <div class="vital-item">
                <div class="vital-value">${data.vitalSigns[0].weight} lbs</div>
                <div class="vital-label">Weight</div>
              </div>
            </div>
          </div>
        ` : ''}

        ${data.visits && data.visits.length > 0 ? `
          <div class="section">
            <h3>Recent Visits</h3>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Department</th>
                  <th>Doctor</th>
                  <th>Chief Complaint</th>
                </tr>
              </thead>
              <tbody>
                ${data.visits.slice(0, 5).map(visit => `
                  <tr>
                    <td>${new Date(visit.visitDate).toLocaleDateString()}</td>
                    <td>${visit.visitType}</td>
                    <td>${visit.department}</td>
                    <td>${visit.doctor}</td>
                    <td>${visit.chiefComplaint}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        ` : ''}

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  private generateMedicalHistoryHTML(data: PatientReportData, options: ReportOptions): string {
    const currentDate = new Date().toLocaleDateString();

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Medical History Report</title>
        <style>
          ${this.getCommonStyles()}
          .timeline-item { margin: 15px 0; padding: 15px; border-left: 3px solid #007bff; background: #f8f9fa; }
          .timeline-date { font-weight: bold; color: #007bff; margin-bottom: 5px; }
        </style>
      </head>
      <body>
        ${this.generateHeader('Medical History Report', currentDate)}
        
        <div class="patient-info">
          <h3>Patient: ${data.patient.firstName} ${data.patient.lastName} (${data.patient.patientId})</h3>
        </div>

        ${data.visits && data.visits.length > 0 ? `
          <div class="section">
            <h3>Visit History</h3>
            ${data.visits.map(visit => `
              <div class="timeline-item">
                <div class="timeline-date">${new Date(visit.visitDate).toLocaleDateString()}</div>
                <p><strong>Type:</strong> ${visit.visitType} | <strong>Department:</strong> ${visit.department}</p>
                <p><strong>Doctor:</strong> ${visit.doctor}</p>
                <p><strong>Chief Complaint:</strong> ${visit.chiefComplaint}</p>
                ${visit.diagnosis ? `<p><strong>Diagnosis:</strong> ${visit.diagnosis}</p>` : ''}
                ${visit.treatment ? `<p><strong>Treatment:</strong> ${visit.treatment}</p>` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${data.treatmentPlans && data.treatmentPlans.length > 0 ? `
          <div class="section">
            <h3>Treatment Plans</h3>
            ${data.treatmentPlans.map(plan => `
              <div class="timeline-item">
                <div class="timeline-date">${new Date(plan.startDate).toLocaleDateString()}</div>
                <p><strong>Plan:</strong> ${plan.planName} | <strong>Status:</strong> ${plan.status}</p>
                ${plan.medications.length > 0 ? `
                  <p><strong>Medications:</strong></p>
                  <ul>
                    ${plan.medications.map(med => `
                      <li>${med.name} - ${med.dosage}, ${med.frequency} (${med.status})</li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  private generateBillingHTML(data: PatientReportData, options: ReportOptions): string {
    const currentDate = new Date().toLocaleDateString();
    const totalAmount = data.bills?.reduce((sum, bill) => sum + bill.totalAmount, 0) || 0;
    const totalPaid = data.bills?.reduce((sum, bill) => sum + bill.paidAmount, 0) || 0;
    const totalOutstanding = data.bills?.reduce((sum, bill) => sum + bill.outstandingAmount, 0) || 0;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Billing Statement</title>
        <style>
          ${this.getCommonStyles()}
          .billing-summary { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .amount-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; text-align: center; }
          .amount-item { padding: 15px; background: white; border-radius: 6px; }
          .amount-value { font-size: 24px; font-weight: bold; }
          .bill-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        </style>
      </head>
      <body>
        ${this.generateHeader('Billing Statement', currentDate)}
        
        <div class="patient-info">
          <h3>Patient: ${data.patient.firstName} ${data.patient.lastName} (${data.patient.patientId})</h3>
        </div>

        <div class="billing-summary">
          <h3>Billing Summary</h3>
          <div class="amount-grid">
            <div class="amount-item">
              <div class="amount-value">$${totalAmount.toFixed(2)}</div>
              <div>Total Billed</div>
            </div>
            <div class="amount-item">
              <div class="amount-value">$${totalPaid.toFixed(2)}</div>
              <div>Total Paid</div>
            </div>
            <div class="amount-item">
              <div class="amount-value">$${totalOutstanding.toFixed(2)}</div>
              <div>Outstanding</div>
            </div>
          </div>
        </div>

        ${data.bills && data.bills.length > 0 ? `
          <div class="section">
            <h3>Bill Details</h3>
            ${data.bills.map(bill => `
              <div class="bill-item">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                  <h4>Bill #${bill.billId}</h4>
                  <span class="status-badge status-${bill.status.toLowerCase().replace(' ', '-')}">${bill.status}</span>
                </div>
                <p><strong>Date:</strong> ${new Date(bill.billDate).toLocaleDateString()}</p>
                
                <table class="data-table" style="margin: 10px 0;">
                  <thead>
                    <tr>
                      <th>Description</th>
                      <th>Qty</th>
                      <th>Unit Price</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${bill.items.map(item => `
                      <tr>
                        <td>${item.description}</td>
                        <td>${item.quantity}</td>
                        <td>$${item.unitPrice.toFixed(2)}</td>
                        <td>$${item.totalPrice.toFixed(2)}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
                
                <div style="text-align: right; margin-top: 10px;">
                  <p><strong>Total Amount: $${bill.totalAmount.toFixed(2)}</strong></p>
                  <p>Paid: $${bill.paidAmount.toFixed(2)}</p>
                  <p><strong>Outstanding: $${bill.outstandingAmount.toFixed(2)}</strong></p>
                </div>
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  private generateTreatmentPlanHTML(data: PatientReportData, options: ReportOptions): string {
    const currentDate = new Date().toLocaleDateString();

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Treatment Plan Report</title>
        <style>
          ${this.getCommonStyles()}
          .plan-item { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
          .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
          .progress-fill { height: 100%; background: #28a745; transition: width 0.3s ease; }
          .medication-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
          .medication-item { padding: 10px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #007bff; }
        </style>
      </head>
      <body>
        ${this.generateHeader('Treatment Plan Report', currentDate)}
        
        <div class="patient-info">
          <h3>Patient: ${data.patient.firstName} ${data.patient.lastName} (${data.patient.patientId})</h3>
        </div>

        ${data.treatmentPlans && data.treatmentPlans.length > 0 ? `
          <div class="section">
            <h3>Treatment Plans</h3>
            ${data.treatmentPlans.map(plan => `
              <div class="plan-item">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                  <h4>${plan.planName}</h4>
                  <span class="status-badge status-${plan.status.toLowerCase().replace(' ', '-')}">${plan.status}</span>
                </div>
                <p><strong>Plan ID:</strong> ${plan.planId}</p>
                <p><strong>Start Date:</strong> ${new Date(plan.startDate).toLocaleDateString()}</p>
                
                ${plan.goals.length > 0 ? `
                  <div style="margin: 15px 0;">
                    <h5>Treatment Goals</h5>
                    ${plan.goals.map(goal => `
                      <div style="margin: 10px 0;">
                        <p><strong>${goal.description}</strong></p>
                        <div style="display: flex; align-items: center; gap: 10px;">
                          <div class="progress-bar" style="flex: 1;">
                            <div class="progress-fill" style="width: ${goal.progress}%;"></div>
                          </div>
                          <span>${goal.progress}%</span>
                          <span class="status-badge status-${goal.status.toLowerCase().replace(' ', '-')}">${goal.status}</span>
                        </div>
                      </div>
                    `).join('')}
                  </div>
                ` : ''}
                
                ${plan.medications.length > 0 ? `
                  <div style="margin: 15px 0;">
                    <h5>Medications</h5>
                    <div class="medication-list">
                      ${plan.medications.map(med => `
                        <div class="medication-item">
                          <p><strong>${med.name}</strong></p>
                          <p>Dosage: ${med.dosage}</p>
                          <p>Frequency: ${med.frequency}</p>
                          <span class="status-badge status-${med.status.toLowerCase()}">${med.status}</span>
                        </div>
                      `).join('')}
                    </div>
                  </div>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  private generateVisitSummaryHTML(data: PatientReportData, options: ReportOptions): string {
    const currentDate = new Date().toLocaleDateString();

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Visit Summary Report</title>
        <style>
          ${this.getCommonStyles()}
          .visit-card { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
          .visit-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        </style>
      </head>
      <body>
        ${this.generateHeader('Visit Summary Report', currentDate)}
        
        <div class="patient-info">
          <h3>Patient: ${data.patient.firstName} ${data.patient.lastName} (${data.patient.patientId})</h3>
        </div>

        ${data.visits && data.visits.length > 0 ? `
          <div class="section">
            <h3>Visit Summary</h3>
            ${data.visits.map(visit => `
              <div class="visit-card">
                <div class="visit-header">
                  <h4>Visit #${visit.visitId}</h4>
                  <span class="date-badge">${new Date(visit.visitDate).toLocaleDateString()}</span>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                  <div>
                    <p><strong>Type:</strong> ${visit.visitType}</p>
                    <p><strong>Department:</strong> ${visit.department}</p>
                    <p><strong>Doctor:</strong> ${visit.doctor}</p>
                  </div>
                  <div>
                    <p><strong>Chief Complaint:</strong> ${visit.chiefComplaint}</p>
                    ${visit.diagnosis ? `<p><strong>Diagnosis:</strong> ${visit.diagnosis}</p>` : ''}
                    ${visit.treatment ? `<p><strong>Treatment:</strong> ${visit.treatment}</p>` : ''}
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        ` : ''}

        ${this.generateFooter()}
      </body>
      </html>
    `;
  }

  private getCommonStyles(): string {
    return `
      body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; line-height: 1.6; }
      .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
      .hospital-name { font-size: 24px; font-weight: bold; color: #007bff; margin: 0; }
      .hospital-info { font-size: 14px; color: #666; margin: 5px 0; }
      .report-title { font-size: 20px; font-weight: bold; margin: 15px 0 5px 0; }
      .report-date { font-size: 14px; color: #666; }
      .section { margin: 30px 0; }
      .patient-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; }
      .data-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
      .data-table th, .data-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
      .data-table th { background: #f8f9fa; font-weight: bold; }
      .data-table tr:nth-child(even) { background: #f9f9f9; }
      .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
      .status-active { background: #d4edda; color: #155724; }
      .status-completed { background: #cce5ff; color: #004085; }
      .status-pending { background: #fff3cd; color: #856404; }
      .status-cancelled { background: #f8d7da; color: #721c24; }
      .date-badge { background: #e9ecef; padding: 5px 10px; border-radius: 15px; font-size: 12px; }
      .footer { margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #666; }
      .vital-value { font-size: 18px; font-weight: bold; color: #007bff; }
      .vital-label { font-size: 12px; color: #666; margin-top: 5px; }
      h3 { color: #007bff; border-bottom: 1px solid #e9ecef; padding-bottom: 5px; }
      h4 { color: #495057; margin: 15px 0 10px 0; }
      h5 { color: #6c757d; margin: 10px 0 5px 0; }
    `;
  }

  private generateHeader(title: string, date: string): string {
    return `
      <div class="header">
        <div class="hospital-name">${this.hospitalInfo.name}</div>
        <div class="hospital-tagline">${this.hospitalInfo.tagline}</div>
        <div class="hospital-info">${this.hospitalInfo.address}</div>
        <div class="hospital-info">Phone: ${this.hospitalInfo.phone} | Email: ${this.hospitalInfo.email}</div>
        <div class="report-title">${title}</div>
        <div class="report-date">Generated on: ${date}</div>
      </div>
    `;
  }

  private generateFooter(): string {
    return `
      <div class="footer">
        <p>This report is confidential and intended for medical use only.</p>
        <p>Generated by ${this.hospitalInfo.name} - ${new Date().toLocaleString()}</p>
      </div>
    `;
  }

  private calculateAge(dateOfBirth: string): number {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  private async convertHTMLToPDF(htmlContent: string): Promise<Blob> {
    // Create a proper HTML document for PDF generation
    const fullHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Hospital Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
          .hospital-name { font-size: 24px; font-weight: bold; color: #007bff; }
          .report-title { font-size: 20px; font-weight: bold; margin: 15px 0; }
          table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f8f9fa; }
          .section { margin: 20px 0; }
          .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        ${htmlContent}
      </body>
      </html>
    `;

    // For demonstration, we'll create a proper PDF-like content
    // In production, you would use libraries like jsPDF, Puppeteer, or html2pdf.js

    try {
      // Try to use html2pdf if available (would need to be installed)
      if (typeof window !== 'undefined' && (window as any).html2pdf) {
        const element = document.createElement('div');
        element.innerHTML = fullHTML;

        const opt = {
          margin: 1,
          filename: 'hospital-report.pdf',
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2 },
          jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
        };

        return await (window as any).html2pdf().set(opt).from(element).outputPdf('blob');
      }
    } catch (error) {
      console.warn('html2pdf not available, using fallback');
    }

    // Fallback: Create a more realistic PDF structure
    const pdfContent = this.createBasicPDF(htmlContent);
    return new Blob([pdfContent], { type: 'application/pdf' });
  }

  private createBasicPDF(htmlContent: string): string {
    // Extract text content from HTML
    const textContent = htmlContent
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 1000); // Limit content

    // Create a basic PDF structure with actual content
    return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
  /Font <<
    /F1 5 0 R
  >>
>>
>>
endobj

4 0 obj
<<
/Length ${textContent.length + 100}
>>
stream
BT
/F1 12 Tf
72 720 Td
(${this.hospitalInfo.name}) Tj
0 -20 Td
(Hospital Report - Generated ${new Date().toLocaleDateString()}) Tj
0 -40 Td
(${textContent.substring(0, 500)}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000273 00000 n
0000000${(400 + textContent.length).toString().padStart(3, '0')} 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
${450 + textContent.length}
%%EOF`;
  }

  // Utility method to download the generated PDF
  downloadPDF(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

export const pdfReportService = new PDFReportService();
export type { PatientReportData, ReportOptions };
