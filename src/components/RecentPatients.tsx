import React, { useState, useEffect } from 'react';
import { Users, Eye, Edit, Calendar, Loader, RefreshCw, AlertCircle } from 'lucide-react';
import { patientAPI } from '../services/apiService';

interface RecentPatient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  assignedDoctor?: {
    firstName: string;
    lastName: string;
    department: string;
  };
  status: string;
  lastVisit?: string;
  priority?: string;
  updatedAt: string;
  createdAt: string;
}

export function RecentPatients() {
  const [recentPatients, setRecentPatients] = useState<RecentPatient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchRecentPatients();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(fetchRecentPatients, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchRecentPatients = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await patientAPI.getAll({
        limit: 5,
        sortBy: 'updatedAt',
        sortOrder: 'desc',
        status: 'Active'
      });

      if (response.success) {
        setRecentPatients(response.data);
        setError(null);
      } else {
        setError('Failed to fetch recent patients');
      }
    } catch (err) {
      console.error('Error fetching recent patients:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch recent patients');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchRecentPatients(true);
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const getTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else if (diffInDays === 1) {
      return '1 day ago';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return diffInWeeks === 1 ? '1 week ago' : `${diffInWeeks} weeks ago`;
    }
  };

  const getDynamicPriority = (patient: RecentPatient): string => {
    const age = calculateAge(patient.dateOfBirth);
    const lastUpdate = new Date(patient.updatedAt);
    const hoursSinceUpdate = (new Date().getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);

    // Priority logic based on age, status, and recent activity
    if (patient.status === 'Admitted' || age > 65 || hoursSinceUpdate < 2) {
      return 'High';
    } else if (patient.status === 'Outpatient' || (age > 50 && age <= 65)) {
      return 'Medium';
    } else {
      return 'Low';
    }
  };

  const getDynamicStatus = (patient: RecentPatient): string => {
    // Use existing status or determine based on recent activity
    if (patient.status) {
      return patient.status;
    }

    const hoursSinceUpdate = (new Date().getTime() - new Date(patient.updatedAt).getTime()) / (1000 * 60 * 60);

    if (hoursSinceUpdate < 24) {
      return 'Active';
    } else {
      return 'Scheduled';
    }
  };
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'Medium': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'Low': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Admitted': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'Outpatient': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'Discharged': return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
      case 'Scheduled': return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
      case 'Active': return 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-300';
      default: return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Patients</h2>
          <Users size={20} className="text-gray-500 dark:text-gray-400" />
        </div>
        <div className="flex items-center justify-center py-8">
          <Loader className="animate-spin mr-2 text-blue-500 dark:text-blue-400" size={20} />
          <span className="text-gray-500 dark:text-gray-400">Loading recent patients...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Patients</h2>
          <Users size={20} className="text-gray-500 dark:text-gray-400" />
        </div>
        <div className="text-center py-8">
          <AlertCircle className="mx-auto mb-2 text-red-500 dark:text-red-400" size={24} />
          <p className="text-red-500 dark:text-red-400 text-sm mb-2">{error}</p>
          <button
            onClick={handleRefresh}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Patients</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50"
            title="Refresh"
          >
            <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
          </button>
          <Users size={20} className="text-gray-500 dark:text-gray-400" />
        </div>
      </div>

      {recentPatients.length === 0 ? (
        <div className="text-center py-8">
          <Users className="mx-auto mb-2 text-gray-400 dark:text-gray-500" size={24} />
          <p className="text-gray-500 dark:text-gray-400 text-sm">No recent patients found</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Patient</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Department</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Status</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Priority</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Last Activity</th>
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentPatients.map((patient) => {
                  const age = calculateAge(patient.dateOfBirth);
                  const priority = getDynamicPriority(patient);
                  const status = getDynamicStatus(patient);
                  const lastActivity = getTimeAgo(patient.updatedAt);
                  const department = patient.assignedDoctor?.department || 'General';

                  return (
                    <tr key={patient._id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            {patient.firstName} {patient.lastName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {patient.patientId} • {age}y, {patient.gender}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{department}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(status)}`}>
                          {status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(priority)}`}>
                          {priority}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-500 dark:text-gray-400">{lastActivity}</td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                            title="View Patient"
                            onClick={() => window.location.href = `/patients?view=${patient._id}`}
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                            title="Edit Patient"
                            onClick={() => window.location.href = `/patients?edit=${patient._id}`}
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
                            title="Schedule Appointment"
                            onClick={() => window.location.href = `/appointments?patient=${patient._id}`}
                          >
                            <Calendar size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          <div className="mt-4 text-center">
            <button
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              onClick={() => window.location.href = '/patients'}
            >
              View All Patients
            </button>
          </div>
        </>
      )}
    </div>
  );
}