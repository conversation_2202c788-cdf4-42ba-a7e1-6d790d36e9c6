import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Send, 
  Mail, 
  Phone, 
  Bell, 
  Calendar, 
  Clock, 
  User, 
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader,
  MessageCircle,
  Smartphone,
  Monitor
} from 'lucide-react';

interface Communication {
  _id: string;
  communicationId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
    email?: string;
    phone: string;
  };
  sender: {
    _id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  type: string;
  method: string;
  priority: string;
  subject: string;
  message: string;
  scheduledAt?: string;
  sentAt?: string;
  deliveredAt?: string;
  readAt?: string;
  status: string;
  deliveryStatus: string;
  responseRequired: boolean;
  responseReceived: boolean;
  responseDate?: string;
  responseContent?: string;
  createdAt: string;
}

interface CommunicationStats {
  totalCommunications: number;
  sentToday: number;
  pendingDelivery: number;
  awaitingResponse: number;
  deliveryRate: number;
  responseRate: number;
}

export function PatientCommunication() {
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [stats, setStats] = useState<CommunicationStats>({
    totalCommunications: 0,
    sentToday: 0,
    pendingDelivery: 0,
    awaitingResponse: 0,
    deliveryRate: 0,
    responseRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterMethod, setFilterMethod] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedCommunication, setSelectedCommunication] = useState<Communication | null>(null);
  const [showNewMessageModal, setShowNewMessageModal] = useState(false);

  useEffect(() => {
    fetchCommunications();
    fetchStats();
  }, [searchTerm, filterType, filterMethod, filterStatus]);

  const fetchCommunications = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockCommunications: Communication[] = [
        {
          _id: '1',
          communicationId: 'COM001234',
          patient: {
            _id: 'p1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'PT001234',
            email: '<EMAIL>',
            phone: '******-0123'
          },
          sender: {
            _id: 's1',
            firstName: 'Dr. Sarah',
            lastName: 'Johnson',
            role: 'Doctor'
          },
          type: 'Appointment Reminder',
          method: 'Email',
          priority: 'Medium',
          subject: 'Upcoming Appointment Reminder',
          message: 'This is a reminder that you have an appointment scheduled for tomorrow at 2:00 PM with Dr. Johnson in the Cardiology department.',
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          sentAt: new Date().toISOString(),
          deliveredAt: new Date().toISOString(),
          status: 'Delivered',
          deliveryStatus: 'Delivered',
          responseRequired: false,
          responseReceived: false,
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          communicationId: 'COM001235',
          patient: {
            _id: 'p2',
            firstName: 'Jane',
            lastName: 'Smith',
            patientId: 'PT001235',
            email: '<EMAIL>',
            phone: '******-0124'
          },
          sender: {
            _id: 's2',
            firstName: 'Nurse Mary',
            lastName: 'Wilson',
            role: 'Nurse'
          },
          type: 'Lab Results',
          method: 'SMS',
          priority: 'High',
          subject: 'Lab Results Available',
          message: 'Your lab results are now available. Please log into the patient portal to view them or call us at (*************.',
          sentAt: new Date().toISOString(),
          status: 'Sent',
          deliveryStatus: 'Pending',
          responseRequired: true,
          responseReceived: false,
          createdAt: new Date().toISOString()
        }
      ];
      setCommunications(mockCommunications);
    } catch (err) {
      setError('Failed to fetch communications');
      console.error('Error fetching communications:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalCommunications: 2456,
        sentToday: 89,
        pendingDelivery: 23,
        awaitingResponse: 45,
        deliveryRate: 94.5,
        responseRate: 67.8
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'Email':
        return <Mail size={16} className="text-blue-600" />;
      case 'SMS':
        return <Smartphone size={16} className="text-green-600" />;
      case 'Phone Call':
        return <Phone size={16} className="text-purple-600" />;
      case 'In-App Notification':
        return <Bell size={16} className="text-orange-600" />;
      case 'Portal Message':
        return <Monitor size={16} className="text-indigo-600" />;
      default:
        return <MessageSquare size={16} className="text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 text-green-800';
      case 'Sent':
        return 'bg-blue-100 text-blue-800';
      case 'Scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'Failed':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'Sent':
        return <Send size={14} className="text-blue-600" />;
      case 'Scheduled':
        return <Clock size={14} className="text-yellow-600" />;
      case 'Failed':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <MessageSquare size={14} className="text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Communication</h1>
          <p className="text-gray-600 mt-1">Manage patient communications and messaging</p>
        </div>
        <button
          onClick={() => setShowNewMessageModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>New Message</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Messages</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCommunications.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Sent Today</p>
              <p className="text-2xl font-bold text-green-600">{stats.sentToday}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Send className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingDelivery}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Awaiting Response</p>
              <p className="text-2xl font-bold text-orange-600">{stats.awaitingResponse}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Delivery Rate</p>
              <p className="text-2xl font-bold text-purple-600">{stats.deliveryRate}%</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Response Rate</p>
              <p className="text-2xl font-bold text-indigo-600">{stats.responseRate}%</p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search communications by patient name, subject, or message..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="Appointment Reminder">Appointment Reminder</option>
              <option value="Treatment Notification">Treatment Notification</option>
              <option value="Follow-up Alert">Follow-up Alert</option>
              <option value="Lab Results">Lab Results</option>
              <option value="General Message">General Message</option>
            </select>
            <select
              value={filterMethod}
              onChange={(e) => setFilterMethod(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Methods</option>
              <option value="Email">Email</option>
              <option value="SMS">SMS</option>
              <option value="Phone Call">Phone Call</option>
              <option value="In-App Notification">In-App</option>
              <option value="Portal Message">Portal</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Delivered">Delivered</option>
              <option value="Sent">Sent</option>
              <option value="Scheduled">Scheduled</option>
              <option value="Failed">Failed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Communications Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Communications</h2>
          <p className="text-gray-600 mt-1">Patient communication history and status</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Communication
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Method & Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Delivery Info
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {communications.map((comm) => (
                <tr key={comm._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        {getMethodIcon(comm.method)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {comm.subject}
                        </div>
                        <div className="text-sm text-gray-500">
                          {comm.communicationId}
                        </div>
                        <div className="text-xs text-gray-400">
                          {new Date(comm.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {comm.patient.firstName} {comm.patient.lastName}
                    </div>
                    <div className="text-sm text-gray-500">ID: {comm.patient.patientId}</div>
                    <div className="text-xs text-gray-400">
                      {comm.method === 'Email' ? comm.patient.email : comm.patient.phone}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 flex items-center">
                      {getMethodIcon(comm.method)}
                      <span className="ml-2">{comm.method}</span>
                    </div>
                    <div className="text-sm text-gray-500">{comm.type}</div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(comm.priority)}`}>
                      {comm.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-xs space-y-1">
                      {comm.sentAt && (
                        <div className="flex items-center">
                          <Send size={12} className="mr-1 text-blue-400" />
                          Sent: {new Date(comm.sentAt).toLocaleString()}
                        </div>
                      )}
                      {comm.deliveredAt && (
                        <div className="flex items-center">
                          <CheckCircle size={12} className="mr-1 text-green-400" />
                          Delivered: {new Date(comm.deliveredAt).toLocaleString()}
                        </div>
                      )}
                      {comm.readAt && (
                        <div className="flex items-center">
                          <Eye size={12} className="mr-1 text-purple-400" />
                          Read: {new Date(comm.readAt).toLocaleString()}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(comm.status)}`}>
                        {getStatusIcon(comm.status)}
                        <span className="ml-1">{comm.status}</span>
                      </span>
                      {comm.responseRequired && (
                        <div className="text-xs text-orange-600">
                          Response {comm.responseReceived ? 'Received' : 'Required'}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedCommunication(comm)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle edit */}}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Edit"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle delete */}}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {communications.length === 0 && !loading && (
          <div className="text-center py-12">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No communications found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by sending a message to a patient.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
