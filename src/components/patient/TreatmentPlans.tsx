import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  User, 
  FileText, 
  Activity, 
  AlertCircle,
  Loader,
  Eye,
  Edit,
  Stethoscope,
  Heart,
  Pill,
  Target,
  TrendingUp,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';

interface TreatmentPlan {
  _id: string;
  planId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  doctor: {
    _id: string;
    firstName: string;
    lastName: string;
    department: string;
  };
  planName: string;
  planType: string;
  startDate: string;
  endDate?: string;
  status: string;
  priority: string;
  primaryCondition: {
    diagnosis: string;
    severity: string;
  };
  goals: Array<{
    goalId: string;
    description: string;
    status: string;
    progress: number;
  }>;
  medications: Array<{
    medicationId: string;
    name: string;
    dosage: string;
    frequency: string;
    status: string;
  }>;
  therapies: Array<{
    therapyId: string;
    type: string;
    description: string;
    status: string;
  }>;
  createdAt: string;
}

interface TreatmentPlanStats {
  totalPlans: number;
  activePlans: number;
  completedPlans: number;
  onHoldPlans: number;
  criticalPlans: number;
}

export function TreatmentPlans() {
  const [treatmentPlans, setTreatmentPlans] = useState<TreatmentPlan[]>([]);
  const [stats, setStats] = useState<TreatmentPlanStats>({
    totalPlans: 0,
    activePlans: 0,
    completedPlans: 0,
    onHoldPlans: 0,
    criticalPlans: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [selectedPlan, setSelectedPlan] = useState<TreatmentPlan | null>(null);
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);

  useEffect(() => {
    fetchTreatmentPlans();
    fetchStats();
  }, [searchTerm, filterStatus, filterPriority]);

  const fetchTreatmentPlans = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockPlans: TreatmentPlan[] = [
        {
          _id: '1',
          planId: 'TP001234',
          patient: {
            _id: 'p1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'PT001234'
          },
          doctor: {
            _id: 'd1',
            firstName: 'Sarah',
            lastName: 'Johnson',
            department: 'Cardiology'
          },
          planName: 'Hypertension Management Plan',
          planType: 'Chronic',
          startDate: new Date().toISOString(),
          status: 'Active',
          priority: 'High',
          primaryCondition: {
            diagnosis: 'Essential Hypertension',
            severity: 'Moderate'
          },
          goals: [
            {
              goalId: 'GL001',
              description: 'Reduce blood pressure to <140/90 mmHg',
              status: 'In Progress',
              progress: 65
            },
            {
              goalId: 'GL002',
              description: 'Weight reduction of 10 lbs',
              status: 'In Progress',
              progress: 40
            }
          ],
          medications: [
            {
              medicationId: 'MD001',
              name: 'Lisinopril',
              dosage: '10mg',
              frequency: 'Once daily',
              status: 'Active'
            },
            {
              medicationId: 'MD002',
              name: 'Hydrochlorothiazide',
              dosage: '25mg',
              frequency: 'Once daily',
              status: 'Active'
            }
          ],
          therapies: [
            {
              therapyId: 'TH001',
              type: 'Physical Therapy',
              description: 'Cardiovascular exercise program',
              status: 'In Progress'
            }
          ],
          createdAt: new Date().toISOString()
        }
      ];
      setTreatmentPlans(mockPlans);
    } catch (err) {
      setError('Failed to fetch treatment plans');
      console.error('Error fetching treatment plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalPlans: 156,
        activePlans: 89,
        completedPlans: 45,
        onHoldPlans: 12,
        criticalPlans: 8
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-blue-100 text-blue-800';
      case 'On Hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'Discontinued':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <Activity size={16} className="text-green-600" />;
      case 'Completed':
        return <CheckCircle size={16} className="text-blue-600" />;
      case 'On Hold':
        return <Pause size={16} className="text-yellow-600" />;
      case 'Discontinued':
      case 'Cancelled':
        return <XCircle size={16} className="text-red-600" />;
      default:
        return <FileText size={16} className="text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Treatment Plans</h1>
          <p className="text-gray-600 mt-1">Comprehensive treatment planning and progress tracking</p>
        </div>
        <button
          onClick={() => setShowNewPlanModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>New Treatment Plan</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Plans</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalPlans}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Plans</p>
              <p className="text-2xl font-bold text-green-600">{stats.activePlans}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-blue-600">{stats.completedPlans}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">On Hold</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.onHoldPlans}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Pause className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-600">{stats.criticalPlans}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search treatment plans by patient name, diagnosis, or plan name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Completed">Completed</option>
              <option value="On Hold">On Hold</option>
              <option value="Discontinued">Discontinued</option>
            </select>
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Priority</option>
              <option value="Critical">Critical</option>
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
        </div>
      </div>

      {/* Treatment Plans Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Treatment Plans</h2>
          <p className="text-gray-600 mt-1">Active and ongoing patient treatment plans</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Doctor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Condition
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {treatmentPlans.map((plan) => (
                <tr key={plan._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Stethoscope size={16} className="text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {plan.planName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {plan.planId} • {plan.planType}
                        </div>
                        <div className="text-xs text-gray-400">
                          Started: {new Date(plan.startDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {plan.patient.firstName} {plan.patient.lastName}
                    </div>
                    <div className="text-sm text-gray-500">ID: {plan.patient.patientId}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      Dr. {plan.doctor.firstName} {plan.doctor.lastName}
                    </div>
                    <div className="text-sm text-gray-500">{plan.doctor.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {plan.primaryCondition.diagnosis}
                    </div>
                    <div className="text-sm text-gray-500">
                      Severity: {plan.primaryCondition.severity}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-xs space-y-1">
                      <div className="flex items-center">
                        <Target size={12} className="mr-1 text-blue-400" />
                        {plan.goals.length} Goals
                      </div>
                      <div className="flex items-center">
                        <Pill size={12} className="mr-1 text-green-400" />
                        {plan.medications.length} Medications
                      </div>
                      <div className="flex items-center">
                        <Heart size={12} className="mr-1 text-purple-400" />
                        {plan.therapies.length} Therapies
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col space-y-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(plan.status)}`}>
                        {getStatusIcon(plan.status)}
                        <span className="ml-1">{plan.status}</span>
                      </span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(plan.priority)}`}>
                        {plan.priority}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedPlan(plan)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle edit */}}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Edit Plan"
                      >
                        <Edit size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {treatmentPlans.length === 0 && !loading && (
          <div className="text-center py-12">
            <Stethoscope className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No treatment plans found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by creating a new treatment plan for a patient.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
