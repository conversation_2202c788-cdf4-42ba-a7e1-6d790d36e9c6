import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Eye, 
  Calendar, 
  FileText, 
  UserPlus, 
  AlertCircle, 
  Loader,
  Phone,
  Mail,
  MapPin,
  Heart,
  Activity,
  Clock,
  Users,
  TrendingUp,
  Download,
  Trash2
} from 'lucide-react';
import { patientAPI } from '../../services/apiService';

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  dateOfBirth: string;
  gender: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
    address?: string;
  };
  insurance?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
    expiryDate?: string;
    copayAmount?: number;
    deductibleAmount?: number;
    coverageType?: string;
  };
  bloodType?: string;
  status: string;
  assignedDoctor?: {
    _id: string;
    firstName: string;
    lastName: string;
    department: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface PatientStats {
  totalPatients: number;
  activePatients: number;
  newPatientsThisMonth: number;
  upcomingAppointments: number;
  criticalAlerts: number;
}

export function PatientOverview() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [stats, setStats] = useState<PatientStats>({
    totalPatients: 0,
    activePatients: 0,
    newPatientsThisMonth: 0,
    upcomingAppointments: 0,
    criticalAlerts: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showPatientModal, setShowPatientModal] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [filters, setFilters] = useState({
    ageRange: { min: '', max: '' },
    gender: '',
    bloodType: '',
    status: '',
    assignedDoctor: '',
    department: '',
    insuranceProvider: '',
    hasChronicConditions: '',
    hasAllergies: '',
    registrationDate: { start: '', end: '' },
    lastVisitDate: { start: '', end: '' }
  });

  useEffect(() => {
    fetchPatients();
    fetchStats();
  }, [searchTerm, filters]);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params: any = { limit: 20 };

      if (searchTerm) params.search = searchTerm;

      // Add advanced filters
      if (filters.ageRange.min) params.ageMin = filters.ageRange.min;
      if (filters.ageRange.max) params.ageMax = filters.ageRange.max;
      if (filters.gender) params.gender = filters.gender;
      if (filters.bloodType) params.bloodType = filters.bloodType;
      if (filters.status) params.status = filters.status;
      if (filters.department) params.department = filters.department;
      if (filters.insuranceProvider) params.insuranceProvider = filters.insuranceProvider;
      if (filters.hasChronicConditions) params.hasChronicConditions = filters.hasChronicConditions;
      if (filters.hasAllergies) params.hasAllergies = filters.hasAllergies;
      if (filters.registrationDate.start) params.registrationStart = filters.registrationDate.start;
      if (filters.registrationDate.end) params.registrationEnd = filters.registrationDate.end;
      if (filters.lastVisitDate.start) params.lastVisitStart = filters.lastVisitDate.start;
      if (filters.lastVisitDate.end) params.lastVisitEnd = filters.lastVisitDate.end;

      const response = await patientAPI.getAll(params);

      if (response.success) {
        setPatients(response.data || []);
      } else {
        throw new Error(response.error || 'Failed to fetch patients');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch patients');
      console.error('Error fetching patients:', err);

      // Fallback to empty array on error
      setPatients([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Fetch real dashboard stats from API
      const response = await patientAPI.getStats();

      if (response.success) {
        setStats(response.data);
      } else {
        // Fallback to calculated stats if API doesn't have stats endpoint
        const allPatientsResponse = await patientAPI.getAll({ limit: 1000 });
        if (allPatientsResponse.success) {
          const allPatients = allPatientsResponse.data || [];
          const currentMonth = new Date().getMonth();
          const currentYear = new Date().getFullYear();

          const newPatientsThisMonth = allPatients.filter(patient => {
            const createdDate = new Date(patient.createdAt);
            return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
          }).length;

          setStats({
            totalPatients: allPatients.length,
            activePatients: allPatients.filter(p => p.status === 'Active').length,
            newPatientsThisMonth,
            upcomingAppointments: 0, // Would need appointments API
            criticalAlerts: allPatients.filter(p => p.medicalHistory?.allergies?.length > 0).length
          });
        }
      }
    } catch (err) {
      console.error('Error fetching stats:', err);
      // Set default stats on error
      setStats({
        totalPatients: 0,
        activePatients: 0,
        newPatientsThisMonth: 0,
        upcomingAppointments: 0,
        criticalAlerts: 0
      });
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const handleViewPatient = (patientId: string) => {
    // Navigate to patient details page
    window.location.href = `/patients/details/${patientId}`;
  };

  const handleEditPatient = (patientId: string) => {
    // Navigate to patient edit page
    window.location.href = `/patients/edit/${patientId}`;
  };

  const handleDeletePatient = async (patientId: string) => {
    if (window.confirm('Are you sure you want to delete this patient?')) {
      try {
        const response = await patientAPI.delete(patientId);
        if (response.success) {
          alert('Patient deleted successfully');
          fetchPatients(); // Refresh the list
          fetchStats(); // Refresh stats
        } else {
          alert('Failed to delete patient');
        }
      } catch (err) {
        console.error('Error deleting patient:', err);
        alert('Error deleting patient');
      }
    }
  };

  const handleAddPatient = () => {
    window.location.href = '/patients/registration';
  };

  const handleApplyFilters = () => {
    fetchPatients();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPatients();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Overview</h1>
          <p className="text-gray-600 mt-1">Comprehensive patient management dashboard</p>
        </div>
        <button
          onClick={handleAddPatient}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <UserPlus size={20} />
          <span>Add New Patient</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Patients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalPatients.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Patients</p>
              <p className="text-2xl font-bold text-green-600">{stats.activePatients.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">New This Month</p>
              <p className="text-2xl font-bold text-blue-600">{stats.newPatientsThisMonth}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Upcoming Appointments</p>
              <p className="text-2xl font-bold text-purple-600">{stats.upcomingAppointments}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
              <p className="text-2xl font-bold text-red-600">{stats.criticalAlerts}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <form onSubmit={handleSearch} className="space-y-4">
          {/* Basic Search */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search patients by name, ID, phone, email, or medical conditions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              type="submit"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
            <button
              type="button"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
                showAdvancedFilters
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Filter size={20} />
              <span>Advanced Filters</span>
            </button>
            <button
              type="button"
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <Download size={20} />
              <span>Export</span>
            </button>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="border-t pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Age Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Age Range</label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filters.ageRange.min}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, min: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={filters.ageRange.max}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, max: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                  <select
                    value={filters.gender}
                    onChange={(e) => setFilters(prev => ({ ...prev, gender: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Genders</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Blood Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Blood Type</label>
                  <select
                    value={filters.bloodType}
                    onChange={(e) => setFilters(prev => ({ ...prev, bloodType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Blood Types</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="Discharged">Discharged</option>
                  </select>
                </div>

                {/* Department */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                  <select
                    value={filters.department}
                    onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Departments</option>
                    <option value="Cardiology">Cardiology</option>
                    <option value="Neurology">Neurology</option>
                    <option value="Orthopedics">Orthopedics</option>
                    <option value="Pediatrics">Pediatrics</option>
                    <option value="General Medicine">General Medicine</option>
                  </select>
                </div>

                {/* Insurance Provider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Insurance</label>
                  <select
                    value={filters.insuranceProvider}
                    onChange={(e) => setFilters(prev => ({ ...prev, insuranceProvider: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Insurance</option>
                    <option value="Blue Cross Blue Shield">Blue Cross Blue Shield</option>
                    <option value="Aetna">Aetna</option>
                    <option value="Medicare">Medicare</option>
                    <option value="Medicaid">Medicaid</option>
                    <option value="Self-Pay">Self-Pay</option>
                  </select>
                </div>

                {/* Chronic Conditions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Chronic Conditions</label>
                  <select
                    value={filters.hasChronicConditions}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasChronicConditions: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Patients</option>
                    <option value="yes">Has Chronic Conditions</option>
                    <option value="no">No Chronic Conditions</option>
                  </select>
                </div>

                {/* Allergies */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Allergies</label>
                  <select
                    value={filters.hasAllergies}
                    onChange={(e) => setFilters(prev => ({ ...prev, hasAllergies: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All Patients</option>
                    <option value="yes">Has Allergies</option>
                    <option value="no">No Known Allergies</option>
                  </select>
                </div>
              </div>

              {/* Date Ranges */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Registration Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Registration Date</label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filters.registrationDate.start}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        registrationDate: { ...prev.registrationDate, start: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="date"
                      value={filters.registrationDate.end}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        registrationDate: { ...prev.registrationDate, end: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Last Visit Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Visit Date</label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filters.lastVisitDate.start}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        lastVisitDate: { ...prev.lastVisitDate, start: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="date"
                      value={filters.lastVisitDate.end}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        lastVisitDate: { ...prev.lastVisitDate, end: e.target.value }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              {/* Filter Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-gray-600">
                  {Object.values(filters).some(value =>
                    typeof value === 'string' ? value !== '' :
                    typeof value === 'object' ? Object.values(value).some(v => v !== '') : false
                  ) && (
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                      Filters Applied
                    </span>
                  )}
                </div>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => setFilters({
                      ageRange: { min: '', max: '' },
                      gender: '',
                      bloodType: '',
                      status: '',
                      assignedDoctor: '',
                      department: '',
                      insuranceProvider: '',
                      hasChronicConditions: '',
                      hasAllergies: '',
                      registrationDate: { start: '', end: '' },
                      lastVisitDate: { start: '', end: '' }
                    })}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    type="button"
                    onClick={handleApplyFilters}
                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Recent Patients */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Recent Patients</h2>
          <p className="text-gray-600 mt-1">Latest patient registrations and updates</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Age/Gender
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned Doctor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {patients.map((patient) => (
                <tr key={patient._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium">
                          {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {patient.firstName} {patient.lastName}
                        </div>
                        <div className="text-sm text-gray-500">ID: {patient.patientId}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Phone size={14} className="mr-1 text-gray-400" />
                      {patient.phone}
                    </div>
                    {patient.email && (
                      <div className="text-sm text-gray-500 flex items-center">
                        <Mail size={14} className="mr-1 text-gray-400" />
                        {patient.email}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {calculateAge(patient.dateOfBirth)} years
                    </div>
                    <div className="text-sm text-gray-500">{patient.gender}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {patient.assignedDoctor ? (
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          Dr. {patient.assignedDoctor.firstName} {patient.assignedDoctor.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{patient.assignedDoctor.department}</div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">Not assigned</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      patient.status === 'Active' 
                        ? 'bg-green-100 text-green-800'
                        : patient.status === 'Inactive'
                        ? 'bg-gray-100 text-gray-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {patient.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewPatient(patient._id)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEditPatient(patient._id)}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Edit Patient"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => window.location.href = '/appointments'}
                        className="text-purple-600 hover:text-purple-900 p-1 rounded"
                        title="Schedule Appointment"
                      >
                        <Calendar size={16} />
                      </button>
                      <button
                        onClick={() => handleDeletePatient(patient._id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete Patient"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {patients.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No patients found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding a new patient to the system.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
