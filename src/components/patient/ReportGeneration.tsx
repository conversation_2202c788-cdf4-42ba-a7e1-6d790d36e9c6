import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  User, 
  Filter, 
  Settings, 
  Loader, 
  CheckCircle, 
  AlertCircle,
  Eye,
  Printer,
  Mail,
  Share2
} from 'lucide-react';
import { pdfReportService, PatientReportData, ReportOptions } from '../../services/pdfReportService';
import { patientAPI } from '../../services/apiService';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'patient-summary' | 'medical-history' | 'treatment-plan' | 'billing-statement' | 'visit-summary';
  icon: React.ComponentType<any>;
  fields: string[];
}

interface GeneratedReport {
  id: string;
  name: string;
  type: string;
  patientName: string;
  generatedAt: string;
  size: string;
  status: 'generating' | 'completed' | 'failed';
}

export function ReportGeneration() {
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [patients, setPatients] = useState<any[]>([]);
  const [reportOptions, setReportOptions] = useState<ReportOptions>({
    reportType: 'patient-summary',
    includeCharts: true,
    dateRange: { start: '', end: '' }
  });
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'patient-summary',
      name: 'Patient Summary Report',
      description: 'Comprehensive overview of patient information, recent visits, and vital signs',
      type: 'patient-summary',
      icon: User,
      fields: ['Patient Info', 'Vital Signs', 'Recent Visits', 'Contact Details']
    },
    {
      id: 'medical-history',
      name: 'Medical History Report',
      description: 'Complete medical history including visits, treatments, and diagnoses',
      type: 'medical-history',
      icon: FileText,
      fields: ['Visit History', 'Treatment Plans', 'Medications', 'Diagnoses']
    },
    {
      id: 'treatment-plan',
      name: 'Treatment Plan Report',
      description: 'Detailed treatment plans with goals, medications, and progress tracking',
      type: 'treatment-plan',
      icon: Settings,
      fields: ['Treatment Goals', 'Medications', 'Progress Tracking', 'Care Team']
    },
    {
      id: 'billing-statement',
      name: 'Billing Statement',
      description: 'Financial summary with bills, payments, and outstanding amounts',
      type: 'billing-statement',
      icon: Download,
      fields: ['Bill Details', 'Payment History', 'Outstanding Amounts', 'Insurance Info']
    },
    {
      id: 'visit-summary',
      name: 'Visit Summary Report',
      description: 'Summary of patient visits with details and outcomes',
      type: 'visit-summary',
      icon: Calendar,
      fields: ['Visit Details', 'Chief Complaints', 'Diagnoses', 'Treatments']
    }
  ];

  useEffect(() => {
    fetchPatients();
    loadGeneratedReports();
  }, []);

  const fetchPatients = async () => {
    try {
      const response = await patientAPI.getAll({ limit: 100 });
      setPatients(response.data || []);
    } catch (err) {
      console.error('Error fetching patients:', err);
    }
  };

  const loadGeneratedReports = () => {
    // Mock data for generated reports
    const mockReports: GeneratedReport[] = [
      {
        id: '1',
        name: 'Patient Summary - John Doe',
        type: 'Patient Summary',
        patientName: 'John Doe',
        generatedAt: new Date().toISOString(),
        size: '2.3 MB',
        status: 'completed'
      },
      {
        id: '2',
        name: 'Medical History - Jane Smith',
        type: 'Medical History',
        patientName: 'Jane Smith',
        generatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        size: '1.8 MB',
        status: 'completed'
      }
    ];
    setGeneratedReports(mockReports);
  };

  const generateReport = async () => {
    if (!selectedTemplate || !selectedPatient) {
      setError('Please select both a template and a patient');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Mock patient data - in real implementation, fetch from API
      const patientData: PatientReportData = {
        patient: {
          firstName: selectedPatient.firstName,
          lastName: selectedPatient.lastName,
          patientId: selectedPatient.patientId,
          dateOfBirth: selectedPatient.dateOfBirth,
          gender: selectedPatient.gender,
          phone: selectedPatient.phone,
          email: selectedPatient.email,
          address: selectedPatient.address || {
            street: '123 Main St',
            city: 'Anytown',
            state: 'ST',
            zipCode: '12345'
          },
          bloodType: selectedPatient.bloodType
        },
        visits: [
          {
            visitId: 'VIS001',
            visitDate: new Date().toISOString(),
            visitType: 'Consultation',
            department: 'Cardiology',
            doctor: 'Dr. Smith',
            chiefComplaint: 'Chest pain',
            diagnosis: 'Hypertension',
            treatment: 'Medication prescribed'
          }
        ],
        vitalSigns: [
          {
            date: new Date().toISOString(),
            temperature: 98.6,
            bloodPressure: { systolic: 120, diastolic: 80 },
            heartRate: 72,
            weight: 150,
            height: 68
          }
        ]
      };

      const options: ReportOptions = {
        ...reportOptions,
        reportType: selectedTemplate.type
      };

      let pdfBlob: Blob;
      const reportName = `${selectedTemplate.name} - ${selectedPatient.firstName} ${selectedPatient.lastName}`;

      switch (selectedTemplate.type) {
        case 'patient-summary':
          pdfBlob = await pdfReportService.generatePatientSummaryReport(patientData, options);
          break;
        case 'medical-history':
          pdfBlob = await pdfReportService.generateMedicalHistoryReport(patientData, options);
          break;
        case 'treatment-plan':
          pdfBlob = await pdfReportService.generateTreatmentPlanReport(patientData, options);
          break;
        case 'billing-statement':
          pdfBlob = await pdfReportService.generateBillingStatement(patientData, options);
          break;
        case 'visit-summary':
          pdfBlob = await pdfReportService.generateVisitSummaryReport(patientData, options);
          break;
        default:
          throw new Error('Invalid report type');
      }

      // Download the PDF
      const filename = `${reportName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdfReportService.downloadPDF(pdfBlob, filename);

      // Add to generated reports list
      const newReport: GeneratedReport = {
        id: Date.now().toString(),
        name: reportName,
        type: selectedTemplate.name,
        patientName: `${selectedPatient.firstName} ${selectedPatient.lastName}`,
        generatedAt: new Date().toISOString(),
        size: `${(pdfBlob.size / (1024 * 1024)).toFixed(1)} MB`,
        status: 'completed'
      };

      setGeneratedReports(prev => [newReport, ...prev]);

    } catch (err) {
      setError('Failed to generate report. Please try again.');
      console.error('Report generation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'generating':
        return <Loader size={16} className="text-blue-600 animate-spin" />;
      case 'failed':
        return <AlertCircle size={16} className="text-red-600" />;
      default:
        return <FileText size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Report Generation</h1>
          <p className="text-gray-600 mt-1">Generate comprehensive patient reports and documents</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Template Selection */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Report Template</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {reportTemplates.map((template) => {
                const IconComponent = template.icon;
                return (
                  <div
                    key={template.id}
                    onClick={() => setSelectedTemplate(template)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <IconComponent size={20} className="text-blue-600" />
                      <h4 className="font-medium text-gray-900">{template.name}</h4>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {template.fields.map((field, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                        >
                          {field}
                        </span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Patient Selection */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Patient</h3>
            <select
              value={selectedPatient?._id || ''}
              onChange={(e) => {
                const patient = patients.find(p => p._id === e.target.value);
                setSelectedPatient(patient);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a patient...</option>
              {patients.map((patient) => (
                <option key={patient._id} value={patient._id}>
                  {patient.firstName} {patient.lastName} - {patient.patientId}
                </option>
              ))}
            </select>
          </div>

          {/* Report Options */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Report Options</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeCharts"
                  checked={reportOptions.includeCharts}
                  onChange={(e) => setReportOptions(prev => ({ ...prev, includeCharts: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="includeCharts" className="text-sm font-medium text-gray-700">
                  Include charts and graphs
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <input
                    type="date"
                    value={reportOptions.dateRange?.start || ''}
                    onChange={(e) => setReportOptions(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange!, start: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <input
                    type="date"
                    value={reportOptions.dateRange?.end || ''}
                    onChange={(e) => setReportOptions(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange!, end: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            {error && (
              <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-400">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">Ready to Generate</h4>
                <p className="text-sm text-gray-600">
                  {selectedTemplate && selectedPatient
                    ? `Generate ${selectedTemplate.name} for ${selectedPatient.firstName} ${selectedPatient.lastName}`
                    : 'Please select a template and patient'
                  }
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowPreview(true)}
                  disabled={!selectedTemplate || !selectedPatient || loading}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <Eye size={16} />
                  <span>Preview</span>
                </button>
                <button
                  onClick={generateReport}
                  disabled={!selectedTemplate || !selectedPatient || loading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {loading ? (
                    <Loader size={16} className="animate-spin" />
                  ) : (
                    <Download size={16} />
                  )}
                  <span>{loading ? 'Generating...' : 'Generate Report'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Generated Reports */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Reports</h3>
          <div className="space-y-3">
            {generatedReports.map((report) => (
              <div key={report.id} className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(report.status)}
                    <span className="text-sm font-medium text-gray-900">{report.type}</span>
                  </div>
                  <span className="text-xs text-gray-500">{report.size}</span>
                </div>
                <p className="text-sm text-gray-600 mb-1">{report.patientName}</p>
                <p className="text-xs text-gray-500">
                  {new Date(report.generatedAt).toLocaleDateString()}
                </p>
                {report.status === 'completed' && (
                  <div className="flex items-center space-x-2 mt-2">
                    <button className="text-blue-600 hover:text-blue-800 p-1">
                      <Download size={14} />
                    </button>
                    <button className="text-green-600 hover:text-green-800 p-1">
                      <Eye size={14} />
                    </button>
                    <button className="text-purple-600 hover:text-purple-800 p-1">
                      <Share2 size={14} />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
