import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Calendar, 
  DollarSign, 
  Activity, 
  Heart, 
  Clock, 
  Target,
  Download,
  Filter,
  Refresh<PERSON><PERSON>,
  Loader,
  <PERSON><PERSON><PERSON><PERSON>cle,
  <PERSON><PERSON><PERSON>,
  Line<PERSON>hart
} from 'lucide-react';

interface AnalyticsData {
  patientDemographics: {
    totalPatients: number;
    ageGroups: Array<{
      range: string;
      count: number;
      percentage: number;
    }>;
    genderDistribution: Array<{
      gender: string;
      count: number;
      percentage: number;
    }>;
  };
  visitPatterns: {
    totalVisits: number;
    monthlyVisits: Array<{
      month: string;
      visits: number;
      revenue: number;
    }>;
    visitTypes: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
    departmentVisits: Array<{
      department: string;
      visits: number;
      avgDuration: number;
    }>;
  };
  treatmentOutcomes: {
    activeTreatments: number;
    completedTreatments: number;
    successRate: number;
    commonConditions: Array<{
      condition: string;
      count: number;
      successRate: number;
    }>;
  };
  financialAnalytics: {
    totalRevenue: number;
    averageBillAmount: number;
    collectionRate: number;
    insuranceBreakdown: Array<{
      provider: string;
      amount: number;
      percentage: number;
    }>;
  };
  healthTrends: {
    chronicConditions: Array<{
      condition: string;
      prevalence: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    vitalSignsTrends: Array<{
      parameter: string;
      average: number;
      trend: 'up' | 'down' | 'stable';
    }>;
  };
}

export function PatientAnalytics() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('last30days');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedTimeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockData: AnalyticsData = {
        patientDemographics: {
          totalPatients: 1247,
          ageGroups: [
            { range: '0-18', count: 156, percentage: 12.5 },
            { range: '19-35', count: 298, percentage: 23.9 },
            { range: '36-50', count: 374, percentage: 30.0 },
            { range: '51-65', count: 287, percentage: 23.0 },
            { range: '65+', count: 132, percentage: 10.6 }
          ],
          genderDistribution: [
            { gender: 'Female', count: 678, percentage: 54.4 },
            { gender: 'Male', count: 569, percentage: 45.6 }
          ]
        },
        visitPatterns: {
          totalVisits: 3456,
          monthlyVisits: [
            { month: 'Jan', visits: 287, revenue: 145000 },
            { month: 'Feb', visits: 312, revenue: 156000 },
            { month: 'Mar', visits: 298, revenue: 149000 },
            { month: 'Apr', visits: 334, revenue: 167000 },
            { month: 'May', visits: 356, revenue: 178000 },
            { month: 'Jun', visits: 289, revenue: 144500 }
          ],
          visitTypes: [
            { type: 'Consultation', count: 1456, percentage: 42.1 },
            { type: 'Follow-up', count: 987, percentage: 28.6 },
            { type: 'Emergency', count: 456, percentage: 13.2 },
            { type: 'Routine Check-up', count: 557, percentage: 16.1 }
          ],
          departmentVisits: [
            { department: 'Cardiology', visits: 456, avgDuration: 45 },
            { department: 'General Medicine', visits: 789, avgDuration: 30 },
            { department: 'Orthopedics', visits: 234, avgDuration: 35 },
            { department: 'Pediatrics', visits: 345, avgDuration: 25 }
          ]
        },
        treatmentOutcomes: {
          activeTreatments: 234,
          completedTreatments: 567,
          successRate: 87.5,
          commonConditions: [
            { condition: 'Hypertension', count: 156, successRate: 92.3 },
            { condition: 'Diabetes', count: 134, successRate: 88.1 },
            { condition: 'Arthritis', count: 98, successRate: 85.7 },
            { condition: 'Asthma', count: 76, successRate: 94.2 }
          ]
        },
        financialAnalytics: {
          totalRevenue: 1250000,
          averageBillAmount: 850,
          collectionRate: 85.5,
          insuranceBreakdown: [
            { provider: 'Blue Cross', amount: 450000, percentage: 36.0 },
            { provider: 'Aetna', amount: 312000, percentage: 25.0 },
            { provider: 'Medicare', amount: 275000, percentage: 22.0 },
            { provider: 'Self-Pay', amount: 213000, percentage: 17.0 }
          ]
        },
        healthTrends: {
          chronicConditions: [
            { condition: 'Hypertension', prevalence: 12.5, trend: 'up' },
            { condition: 'Diabetes', prevalence: 10.7, trend: 'stable' },
            { condition: 'Heart Disease', prevalence: 8.3, trend: 'down' },
            { condition: 'Obesity', prevalence: 15.2, trend: 'up' }
          ],
          vitalSignsTrends: [
            { parameter: 'Blood Pressure', average: 125, trend: 'stable' },
            { parameter: 'Heart Rate', average: 72, trend: 'stable' },
            { parameter: 'BMI', average: 26.8, trend: 'up' },
            { parameter: 'Cholesterol', average: 195, trend: 'down' }
          ]
        }
      };
      setAnalyticsData(mockData);
    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await fetchAnalyticsData();
    setRefreshing(false);
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} className="text-red-500" />;
      case 'down':
        return <TrendingDown size={16} className="text-green-500" />;
      case 'stable':
        return <Activity size={16} className="text-blue-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Failed to load analytics</h3>
        <p className="mt-1 text-sm text-gray-500">Please try refreshing the page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Analytics</h1>
          <p className="text-gray-600 mt-1">Comprehensive insights and data visualization</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="last7days">Last 7 Days</option>
            <option value="last30days">Last 30 Days</option>
            <option value="last3months">Last 3 Months</option>
            <option value="last6months">Last 6 Months</option>
            <option value="lastyear">Last Year</option>
          </select>
          <button
            onClick={refreshData}
            disabled={refreshing}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
          >
            <RefreshCw size={20} className={refreshing ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
            <Download size={20} />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Patients</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.patientDemographics.totalPatients.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp size={14} className="mr-1" />
                +5.2% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Visits</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.visitPatterns.totalVisits.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp size={14} className="mr-1" />
                +8.1% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.financialAnalytics.totalRevenue)}</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp size={14} className="mr-1" />
                +12.3% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{analyticsData.treatmentOutcomes.successRate}%</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp size={14} className="mr-1" />
                +2.1% from last month
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Age Distribution */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Age Distribution</h3>
            <PieChart size={20} className="text-gray-400" />
          </div>
          <div className="space-y-3">
            {analyticsData.patientDemographics.ageGroups.map((group, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 bg-blue-${(index + 1) * 100}`}></div>
                  <span className="text-sm font-medium text-gray-700">{group.range} years</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{group.count}</div>
                  <div className="text-xs text-gray-500">{group.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Visit Types */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Visit Types</h3>
            <BarChart3 size={20} className="text-gray-400" />
          </div>
          <div className="space-y-3">
            {analyticsData.visitPatterns.visitTypes.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 bg-green-${(index + 1) * 100}`}></div>
                  <span className="text-sm font-medium text-gray-700">{type.type}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{type.count}</div>
                  <div className="text-xs text-gray-500">{type.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Common Conditions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Common Conditions</h3>
            <Heart size={20} className="text-gray-400" />
          </div>
          <div className="space-y-4">
            {analyticsData.treatmentOutcomes.commonConditions.map((condition, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{condition.condition}</span>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">{condition.count} patients</div>
                    <div className="text-xs text-green-600">{condition.successRate}% success</div>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(condition.count / 200) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Health Trends */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Health Trends</h3>
            <LineChart size={20} className="text-gray-400" />
          </div>
          <div className="space-y-3">
            {analyticsData.healthTrends.chronicConditions.map((trend, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-700">{trend.condition}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{trend.prevalence}%</span>
                  {getTrendIcon(trend.trend)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Department Performance */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Department Performance</h3>
          <BarChart3 size={20} className="text-gray-400" />
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Visits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analyticsData.visitPatterns.departmentVisits.map((dept, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {dept.department}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {dept.visits}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Clock size={14} className="mr-1 text-gray-400" />
                      {dept.avgDuration} min
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${(dept.visits / 800) * 100}%` }}
                      ></div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
