import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  User, 
  FileText, 
  Activity, 
  AlertCircle,
  Loader,
  Eye,
  Edit,
  Stethoscope,
  Heart,
  Thermometer,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { patientVisitAPI } from '../../services/apiService';

interface PatientVisit {
  _id: string;
  visitId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  doctor: {
    _id: string;
    firstName: string;
    lastName: string;
    department: string;
  };
  visitDate: string;
  visitTime: string;
  visitType: string;
  department: string;
  chiefComplaint: string;
  status: string;
  visitDuration: number;
  physicalExamination?: {
    vitalSigns?: {
      temperature: number;
      bloodPressure: {
        systolic: number;
        diastolic: number;
      };
      heartRate: number;
      respiratoryRate: number;
      oxygenSaturation: number;
    };
  };
  assessment?: {
    primaryDiagnosis: {
      description: string;
    };
  };
  createdAt: string;
}

interface VisitStats {
  totalVisits: number;
  todayVisits: number;
  inProgressVisits: number;
  completedVisits: number;
  averageVisitDuration: number;
}

export function VisitManagement() {
  const [visits, setVisits] = useState<PatientVisit[]>([]);
  const [stats, setStats] = useState<VisitStats>({
    totalVisits: 0,
    todayVisits: 0,
    inProgressVisits: 0,
    completedVisits: 0,
    averageVisitDuration: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDate, setFilterDate] = useState('');
  const [selectedVisit, setSelectedVisit] = useState<PatientVisit | null>(null);
  const [showNewVisitModal, setShowNewVisitModal] = useState(false);

  useEffect(() => {
    fetchVisits();
    fetchStats();
  }, [searchTerm, filterStatus, filterDate]);

  const fetchVisits = async () => {
    try {
      setLoading(true);
      const params: any = { limit: 20 };

      if (searchTerm) params.search = searchTerm;
      if (filterStatus !== 'all') params.status = filterStatus;
      if (filterDate) params.startDate = filterDate;

      const response = await patientVisitAPI.getAll(params);
      setVisits(response.data || []);
    } catch (err) {
      setError('Failed to fetch visits');
      console.error('Error fetching visits:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await patientVisitAPI.getStats();
      setStats(response.data || {
        totalVisits: 0,
        todayVisits: 0,
        inProgressVisits: 0,
        completedVisits: 0,
        averageVisitDuration: 0
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
      // Set default stats on error
      setStats({
        totalVisits: 0,
        todayVisits: 0,
        inProgressVisits: 0,
        completedVisits: 0,
        averageVisitDuration: 0
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      case 'No Show':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVisitTypeIcon = (type: string) => {
    switch (type) {
      case 'Consultation':
        return <Stethoscope size={16} />;
      case 'Follow-up':
        return <TrendingUp size={16} />;
      case 'Emergency':
        return <AlertCircle size={16} />;
      case 'Routine Check-up':
        return <Heart size={16} />;
      default:
        return <FileText size={16} />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Visit Management</h1>
          <p className="text-gray-600 mt-1">Track and manage patient visits and consultations</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchVisits}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
          >
            <RefreshCw size={20} />
            <span>Refresh</span>
          </button>
          <button
            onClick={() => setShowNewVisitModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>New Visit</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Visits</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalVisits.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Today's Visits</p>
              <p className="text-2xl font-bold text-blue-600">{stats.todayVisits}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.inProgressVisits}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{stats.completedVisits}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Duration</p>
              <p className="text-2xl font-bold text-purple-600">{stats.averageVisitDuration}m</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search visits by patient name, ID, or complaint..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Scheduled">Scheduled</option>
              <option value="In Progress">In Progress</option>
              <option value="Completed">Completed</option>
              <option value="Cancelled">Cancelled</option>
            </select>
            <input
              type="date"
              value={filterDate}
              onChange={(e) => setFilterDate(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={fetchVisits}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <Loader size={16} className="animate-spin" />
              ) : (
                <Search size={16} />
              )}
              <span>Load Data</span>
            </button>
          </div>
        </div>
      </div>

      {/* Visits Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Patient Visits</h2>
          <p className="text-gray-600 mt-1">Recent and ongoing patient consultations</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Visit Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Doctor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Chief Complaint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vital Signs
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {visits.map((visit) => (
                <tr key={visit._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        {getVisitTypeIcon(visit.visitType)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {visit.visitId}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(visit.visitDate).toLocaleDateString()} at {visit.visitTime}
                        </div>
                        <div className="text-xs text-gray-400">
                          {visit.visitType} • {visit.department}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {visit.patient.firstName} {visit.patient.lastName}
                    </div>
                    <div className="text-sm text-gray-500">ID: {visit.patient.patientId}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      Dr. {visit.doctor.firstName} {visit.doctor.lastName}
                    </div>
                    <div className="text-sm text-gray-500">{visit.doctor.department}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {visit.chiefComplaint}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {visit.physicalExamination?.vitalSigns ? (
                      <div className="text-xs space-y-1">
                        <div className="flex items-center">
                          <Thermometer size={12} className="mr-1 text-red-400" />
                          {visit.physicalExamination.vitalSigns.temperature}°F
                        </div>
                        <div className="flex items-center">
                          <Heart size={12} className="mr-1 text-red-400" />
                          {visit.physicalExamination.vitalSigns.heartRate} bpm
                        </div>
                        <div className="text-gray-500">
                          BP: {visit.physicalExamination.vitalSigns.bloodPressure.systolic}/
                          {visit.physicalExamination.vitalSigns.bloodPressure.diastolic}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">Not recorded</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(visit.status)}`}>
                      {visit.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedVisit(visit)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle edit */}}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Edit Visit"
                      >
                        <Edit size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {visits.length === 0 && !loading && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No visits found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by creating a new patient visit.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
