import React, { useState, useEffect } from 'react';
import { 
  Upload, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  FileText, 
  Image, 
  File, 
  Calendar, 
  User,
  AlertCircle,
  Loader,
  Plus,
  FolderOpen,
  Shield,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PatientDocument {
  _id: string;
  documentId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  documentName: string;
  documentType: string;
  category: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  fileUrl: string;
  thumbnailUrl?: string;
  description?: string;
  keywords: string[];
  tags: string[];
  clinicalRelevance: string;
  isConfidential: boolean;
  isCritical: boolean;
  documentDate: string;
  expiryDate?: string;
  sourceType: string;
  uploadedBy: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  reviewStatus: string;
  accessLevel: string;
  version: number;
  isLatestVersion: boolean;
  status: string;
  createdAt: string;
}

interface DocumentStats {
  totalDocuments: number;
  pendingReview: number;
  confidentialDocs: number;
  criticalDocs: number;
  expiringSoon: number;
  storageUsed: number;
}

export function PatientDocuments() {
  const [documents, setDocuments] = useState<PatientDocument[]>([]);
  const [stats, setStats] = useState<DocumentStats>({
    totalDocuments: 0,
    pendingReview: 0,
    confidentialDocs: 0,
    criticalDocs: 0,
    expiringSoon: 0,
    storageUsed: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [selectedDocument, setSelectedDocument] = useState<PatientDocument | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);

  useEffect(() => {
    fetchDocuments();
    fetchStats();
  }, [searchTerm, filterCategory, filterType]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockDocuments: PatientDocument[] = [
        {
          _id: '1',
          documentId: 'DOC001234',
          patient: {
            _id: 'p1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'PT001234'
          },
          documentName: 'Blood Test Results - Complete Panel',
          documentType: 'Lab Result',
          category: 'Clinical',
          fileName: 'blood_test_results_20241213.pdf',
          fileSize: 2048576, // 2MB
          fileType: 'application/pdf',
          fileUrl: '/documents/blood_test_results_20241213.pdf',
          description: 'Complete blood panel including CBC, lipid profile, and glucose levels',
          keywords: ['blood test', 'lab results', 'CBC', 'lipid profile'],
          tags: ['routine', 'annual checkup'],
          clinicalRelevance: 'High',
          isConfidential: false,
          isCritical: true,
          documentDate: new Date().toISOString(),
          sourceType: 'Internal',
          uploadedBy: {
            _id: 'u1',
            firstName: 'Dr. Sarah',
            lastName: 'Johnson'
          },
          reviewStatus: 'Approved',
          accessLevel: 'Restricted',
          version: 1,
          isLatestVersion: true,
          status: 'Active',
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          documentId: 'DOC001235',
          patient: {
            _id: 'p1',
            firstName: 'John',
            lastName: 'Doe',
            patientId: 'PT001234'
          },
          documentName: 'Chest X-Ray Report',
          documentType: 'Imaging Study',
          category: 'Clinical',
          fileName: 'chest_xray_20241213.jpg',
          fileSize: 5242880, // 5MB
          fileType: 'image/jpeg',
          fileUrl: '/documents/chest_xray_20241213.jpg',
          thumbnailUrl: '/documents/thumbnails/chest_xray_20241213_thumb.jpg',
          description: 'Chest X-ray showing clear lungs with no abnormalities',
          keywords: ['chest x-ray', 'imaging', 'lungs'],
          tags: ['routine', 'clear'],
          clinicalRelevance: 'Medium',
          isConfidential: false,
          isCritical: false,
          documentDate: new Date().toISOString(),
          sourceType: 'Internal',
          uploadedBy: {
            _id: 'u2',
            firstName: 'Dr. Michael',
            lastName: 'Brown'
          },
          reviewStatus: 'Pending',
          accessLevel: 'Restricted',
          version: 1,
          isLatestVersion: true,
          status: 'Active',
          createdAt: new Date().toISOString()
        }
      ];
      setDocuments(mockDocuments);
    } catch (err) {
      setError('Failed to fetch documents');
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      setStats({
        totalDocuments: 1247,
        pendingReview: 23,
        confidentialDocs: 156,
        criticalDocs: 45,
        expiringSoon: 8,
        storageUsed: 2.5 // GB
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const getDocumentIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image size={16} className="text-green-600" />;
    } else if (fileType === 'application/pdf') {
      return <FileText size={16} className="text-red-600" />;
    } else {
      return <File size={16} className="text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Clinical':
        return 'bg-blue-100 text-blue-800';
      case 'Administrative':
        return 'bg-gray-100 text-gray-800';
      case 'Legal':
        return 'bg-purple-100 text-purple-800';
      case 'Insurance':
        return 'bg-green-100 text-green-800';
      case 'Personal':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getReviewStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Rejected':
        return 'bg-red-100 text-red-800';
      case 'Requires Update':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getReviewStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'Pending':
        return <Clock size={14} className="text-yellow-600" />;
      case 'Rejected':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Documents</h1>
          <p className="text-gray-600 mt-1">Comprehensive document management and organization</p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Upload size={20} />
          <span>Upload Document</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Documents</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDocuments.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingReview}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Confidential</p>
              <p className="text-2xl font-bold text-red-600">{stats.confidentialDocs}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-orange-600">{stats.criticalDocs}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-bold text-purple-600">{stats.expiringSoon}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Storage Used</p>
              <p className="text-2xl font-bold text-indigo-600">{stats.storageUsed} GB</p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
              <FolderOpen className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search documents by name, type, keywords, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              <option value="Clinical">Clinical</option>
              <option value="Administrative">Administrative</option>
              <option value="Legal">Legal</option>
              <option value="Insurance">Insurance</option>
              <option value="Personal">Personal</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="Medical Report">Medical Report</option>
              <option value="Lab Result">Lab Result</option>
              <option value="Imaging Study">Imaging Study</option>
              <option value="Prescription">Prescription</option>
              <option value="Consent Form">Consent Form</option>
            </select>
          </div>
        </div>
      </div>

      {/* Documents Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Patient Documents</h2>
          <p className="text-gray-600 mt-1">Organized medical records and documentation</p>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Document
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Patient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type & Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {documents.map((document) => (
                <tr key={document._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        {getDocumentIcon(document.fileType)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {document.documentName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {document.documentId}
                        </div>
                        <div className="text-xs text-gray-400">
                          {formatFileSize(document.fileSize)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {document.patient.firstName} {document.patient.lastName}
                    </div>
                    <div className="text-sm text-gray-500">ID: {document.patient.patientId}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {document.documentType}
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(document.category)}`}>
                      {document.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-xs space-y-1">
                      <div className="flex items-center">
                        <Calendar size={12} className="mr-1 text-gray-400" />
                        {new Date(document.documentDate).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <User size={12} className="mr-1 text-gray-400" />
                        {document.uploadedBy.firstName} {document.uploadedBy.lastName}
                      </div>
                      <div className="flex items-center space-x-1">
                        {document.isConfidential && (
                          <Shield size={12} className="text-red-500" title="Confidential" />
                        )}
                        {document.isCritical && (
                          <AlertCircle size={12} className="text-orange-500" title="Critical" />
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getReviewStatusColor(document.reviewStatus)}`}>
                      {getReviewStatusIcon(document.reviewStatus)}
                      <span className="ml-1">{document.reviewStatus}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedDocument(document)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="View Document"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle download */}}
                        className="text-green-600 hover:text-green-900 p-1 rounded"
                        title="Download"
                      >
                        <Download size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle edit */}}
                        className="text-yellow-600 hover:text-yellow-900 p-1 rounded"
                        title="Edit Details"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => {/* Handle delete */}}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {documents.length === 0 && !loading && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by uploading patient documents to the system.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
