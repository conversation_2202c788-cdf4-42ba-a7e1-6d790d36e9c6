import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  AlertTriangle, 
  Heart, 
  Thermometer, 
  Activity, 
  User, 
  ArrowRight, 
  CheckCircle, 
  XCircle,
  Timer,
  Stethoscope,
  Eye,
  Edit,
  Plus,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';

interface TriagePatient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  age: number;
  gender: string;
  phone: string;
  arrivalTime: string;
  chiefComplaint: string;
  vitalSigns: {
    temperature: number;
    bloodPressure: { systolic: number; diastolic: number };
    heartRate: number;
    respiratoryRate: number;
    oxygenSaturation: number;
    painScale: number;
  };
  triageLevel: 1 | 2 | 3 | 4 | 5;
  triageCategory: string;
  symptoms: string[];
  allergies: string[];
  currentMedications: string[];
  triageNotes: string;
  assignedNurse: string;
  status: 'waiting' | 'in-triage' | 'triaged' | 'assigned-to-doctor' | 'in-consultation' | 'completed';
  estimatedWaitTime: number;
  actualWaitTime?: number;
  priority: 'critical' | 'urgent' | 'less-urgent' | 'non-urgent' | 'fast-track';
  department: string;
  assignedDoctor?: string;
  roomNumber?: string;
  createdAt: string;
  updatedAt: string;
}

interface TriageStats {
  totalPatients: number;
  waitingPatients: number;
  inTriagePatients: number;
  averageWaitTime: number;
  criticalPatients: number;
  urgentPatients: number;
}

export function TriageManagement() {
  const [patients, setPatients] = useState<TriagePatient[]>([]);
  const [stats, setStats] = useState<TriageStats>({
    totalPatients: 0,
    waitingPatients: 0,
    inTriagePatients: 0,
    averageWaitTime: 0,
    criticalPatients: 0,
    urgentPatients: 0
  });
  const [selectedPatient, setSelectedPatient] = useState<TriagePatient | null>(null);
  const [showTriageModal, setShowTriageModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');

  const triageLevels = [
    { level: 1, name: 'Resuscitation', color: 'bg-red-600', description: 'Immediate life-threatening', maxWait: 0 },
    { level: 2, name: 'Emergency', color: 'bg-orange-500', description: 'Imminently life-threatening', maxWait: 10 },
    { level: 3, name: 'Urgent', color: 'bg-yellow-500', description: 'Potentially life-threatening', maxWait: 30 },
    { level: 4, name: 'Less Urgent', color: 'bg-green-500', description: 'Potentially serious', maxWait: 60 },
    { level: 5, name: 'Non-urgent', color: 'bg-blue-500', description: 'Less serious', maxWait: 120 }
  ];

  const statusOptions = [
    'waiting', 'in-triage', 'triaged', 'assigned-to-doctor', 'in-consultation', 'completed'
  ];

  useEffect(() => {
    fetchTriagePatients();
    fetchTriageStats();
    
    // Set up real-time updates
    const interval = setInterval(() => {
      fetchTriagePatients();
      fetchTriageStats();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchTriagePatients = async () => {
    try {
      setLoading(true);
      
      // Try to fetch from API first
      try {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (filterStatus !== 'all') params.append('status', filterStatus);
        if (filterPriority !== 'all') params.append('priority', filterPriority);
        
        const response = await fetch(`/api/triage/patients?${params}`);
        if (response.ok) {
          const data = await response.json();
          setPatients(data.patients || []);
          return;
        }
      } catch (apiError) {
        console.warn('Triage API not available, using fallback data');
      }
      
      // Fallback to mock data
      const mockPatients: TriagePatient[] = [
        {
          _id: '1',
          patientId: 'PT001234',
          firstName: 'John',
          lastName: 'Doe',
          age: 45,
          gender: 'Male',
          phone: '******-0123',
          arrivalTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          chiefComplaint: 'Chest pain and shortness of breath',
          vitalSigns: {
            temperature: 98.6,
            bloodPressure: { systolic: 160, diastolic: 95 },
            heartRate: 110,
            respiratoryRate: 22,
            oxygenSaturation: 94,
            painScale: 8
          },
          triageLevel: 2,
          triageCategory: 'Cardiovascular',
          symptoms: ['Chest pain', 'Shortness of breath', 'Sweating'],
          allergies: ['Penicillin'],
          currentMedications: ['Lisinopril', 'Metformin'],
          triageNotes: 'Patient presents with acute chest pain, possible cardiac event',
          assignedNurse: 'Nurse Mary Wilson',
          status: 'triaged',
          estimatedWaitTime: 10,
          priority: 'urgent',
          department: 'Emergency Medicine',
          assignedDoctor: 'Dr. Sarah Johnson',
          roomNumber: 'ER-2',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: '2',
          patientId: 'PT001235',
          firstName: 'Jane',
          lastName: 'Smith',
          age: 28,
          gender: 'Female',
          phone: '******-0124',
          arrivalTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          chiefComplaint: 'Severe headache and nausea',
          vitalSigns: {
            temperature: 99.2,
            bloodPressure: { systolic: 140, diastolic: 85 },
            heartRate: 88,
            respiratoryRate: 18,
            oxygenSaturation: 98,
            painScale: 7
          },
          triageLevel: 3,
          triageCategory: 'Neurological',
          symptoms: ['Severe headache', 'Nausea', 'Light sensitivity'],
          allergies: [],
          currentMedications: ['Birth control'],
          triageNotes: 'Sudden onset severe headache, rule out migraine vs secondary causes',
          assignedNurse: 'Nurse John Smith',
          status: 'in-triage',
          estimatedWaitTime: 25,
          priority: 'less-urgent',
          department: 'Emergency Medicine',
          createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: '3',
          patientId: 'PT001236',
          firstName: 'Robert',
          lastName: 'Johnson',
          age: 65,
          gender: 'Male',
          phone: '******-0125',
          arrivalTime: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          chiefComplaint: 'Fall with possible hip fracture',
          vitalSigns: {
            temperature: 98.4,
            bloodPressure: { systolic: 130, diastolic: 80 },
            heartRate: 95,
            respiratoryRate: 16,
            oxygenSaturation: 96,
            painScale: 9
          },
          triageLevel: 3,
          triageCategory: 'Orthopedic',
          symptoms: ['Hip pain', 'Unable to bear weight', 'Swelling'],
          allergies: ['Codeine'],
          currentMedications: ['Atorvastatin', 'Amlodipine'],
          triageNotes: 'Fall at home, severe hip pain, X-ray ordered',
          assignedNurse: 'Nurse Mary Wilson',
          status: 'assigned-to-doctor',
          estimatedWaitTime: 20,
          actualWaitTime: 45,
          priority: 'urgent',
          department: 'Orthopedics',
          assignedDoctor: 'Dr. Michael Brown',
          roomNumber: 'ER-5',
          createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      
      setPatients(mockPatients);
    } catch (err) {
      setError('Failed to fetch triage patients');
      console.error('Error fetching triage patients:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchTriageStats = async () => {
    try {
      // Try to fetch from API first
      try {
        const response = await fetch('/api/triage/stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data.stats);
          return;
        }
      } catch (apiError) {
        console.warn('Triage stats API not available, calculating from patient data');
      }
      
      // Calculate stats from patient data
      const totalPatients = patients.length;
      const waitingPatients = patients.filter(p => p.status === 'waiting').length;
      const inTriagePatients = patients.filter(p => p.status === 'in-triage').length;
      const criticalPatients = patients.filter(p => p.triageLevel <= 2).length;
      const urgentPatients = patients.filter(p => p.triageLevel === 3).length;
      
      const totalWaitTime = patients.reduce((sum, p) => sum + (p.actualWaitTime || p.estimatedWaitTime), 0);
      const averageWaitTime = totalPatients > 0 ? Math.round(totalWaitTime / totalPatients) : 0;
      
      setStats({
        totalPatients,
        waitingPatients,
        inTriagePatients,
        averageWaitTime,
        criticalPatients,
        urgentPatients
      });
    } catch (err) {
      console.error('Error calculating triage stats:', err);
    }
  };

  const getTriageLevelInfo = (level: number) => {
    return triageLevels.find(t => t.level === level) || triageLevels[4];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-gray-100 text-gray-800';
      case 'in-triage':
        return 'bg-blue-100 text-blue-800';
      case 'triaged':
        return 'bg-green-100 text-green-800';
      case 'assigned-to-doctor':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-consultation':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-gray-100 text-gray-600';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getWaitTimeColor = (waitTime: number, triageLevel: number) => {
    const maxWait = getTriageLevelInfo(triageLevel).maxWait;
    if (waitTime > maxWait * 1.5) return 'text-red-600';
    if (waitTime > maxWait) return 'text-orange-600';
    return 'text-green-600';
  };

  const updatePatientStatus = async (patientId: string, newStatus: string) => {
    try {
      // Update patient status - replace with actual API call
      setPatients(prev => prev.map(patient => 
        patient._id === patientId 
          ? { ...patient, status: newStatus as any, updatedAt: new Date().toISOString() }
          : patient
      ));
      
      // Refresh stats
      fetchTriageStats();
    } catch (err) {
      console.error('Error updating patient status:', err);
    }
  };

  const calculateWaitTime = (arrivalTime: string) => {
    const arrival = new Date(arrivalTime);
    const now = new Date();
    return Math.floor((now.getTime() - arrival.getTime()) / (1000 * 60)); // minutes
  };

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = searchTerm === '' || 
      patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.patientId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.chiefComplaint.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || patient.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || patient.priority === filterPriority;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Sort patients by triage level (most urgent first) and then by arrival time
  const sortedPatients = filteredPatients.sort((a, b) => {
    if (a.triageLevel !== b.triageLevel) {
      return a.triageLevel - b.triageLevel;
    }
    return new Date(a.arrivalTime).getTime() - new Date(b.arrivalTime).getTime();
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Triage Management</h1>
          <p className="text-gray-600 mt-1">Emergency department patient triage and queue management</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchTriagePatients}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
          >
            <RefreshCw size={20} />
            <span>Refresh</span>
          </button>
          <button
            onClick={() => setShowTriageModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>New Triage</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Patients</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalPatients}</p>
            </div>
            <User className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Waiting</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.waitingPatients}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Triage</p>
              <p className="text-2xl font-bold text-blue-600">{stats.inTriagePatients}</p>
            </div>
            <Activity className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-600">{stats.criticalPatients}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Urgent</p>
              <p className="text-2xl font-bold text-orange-600">{stats.urgentPatients}</p>
            </div>
            <Heart className="h-8 w-8 text-orange-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Wait</p>
              <p className="text-2xl font-bold text-green-600">{stats.averageWaitTime}m</p>
            </div>
            <Timer className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search by patient name, ID, or complaint..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              {statusOptions.map(status => (
                <option key={status} value={status}>
                  {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          <div>
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Priorities</option>
              <option value="critical">Critical</option>
              <option value="urgent">Urgent</option>
              <option value="less-urgent">Less Urgent</option>
              <option value="non-urgent">Non-urgent</option>
              <option value="fast-track">Fast Track</option>
            </select>
          </div>
        </div>
      </div>

      {/* Triage Queue */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Triage Queue</h3>
          <p className="text-sm text-gray-600 mt-1">Patients sorted by triage level and arrival time</p>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : sortedPatients.length === 0 ? (
          <div className="text-center py-8">
            <User className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No patients in triage</h3>
            <p className="mt-1 text-sm text-gray-500">
              No patients match the current filter criteria.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {sortedPatients.map((patient) => {
              const triageInfo = getTriageLevelInfo(patient.triageLevel);
              const currentWaitTime = calculateWaitTime(patient.arrivalTime);
              
              return (
                <div
                  key={patient._id}
                  className="p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {/* Triage Level Badge */}
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${triageInfo.color}`}>
                        {patient.triageLevel}
                      </div>
                      
                      {/* Patient Info */}
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {patient.firstName} {patient.lastName}
                        </h4>
                        <p className="text-sm text-gray-600">
                          ID: {patient.patientId} • Age: {patient.age} • {patient.gender}
                        </p>
                        <p className="text-sm text-gray-600 mt-1">
                          <strong>Chief Complaint:</strong> {patient.chiefComplaint}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      {/* Vital Signs */}
                      <div className="text-center">
                        <div className="flex items-center space-x-2 text-sm">
                          <Thermometer size={14} className="text-red-500" />
                          <span>{patient.vitalSigns.temperature}°F</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm mt-1">
                          <Heart size={14} className="text-red-500" />
                          <span>{patient.vitalSigns.heartRate} bpm</span>
                        </div>
                      </div>

                      {/* Wait Time */}
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-700">Wait Time</p>
                        <p className={`text-lg font-bold ${getWaitTimeColor(currentWaitTime, patient.triageLevel)}`}>
                          {currentWaitTime}m
                        </p>
                      </div>

                      {/* Status */}
                      <div className="text-center">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(patient.status)}`}>
                          {patient.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                        {patient.roomNumber && (
                          <p className="text-xs text-gray-500 mt-1">Room: {patient.roomNumber}</p>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedPatient(patient)}
                          className="text-blue-600 hover:text-blue-800 p-2"
                          title="View Details"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => setShowTriageModal(true)}
                          className="text-green-600 hover:text-green-800 p-2"
                          title="Edit Triage"
                        >
                          <Edit size={16} />
                        </button>
                        {patient.status === 'triaged' && (
                          <button
                            onClick={() => updatePatientStatus(patient._id, 'assigned-to-doctor')}
                            className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                          >
                            Assign Doctor
                          </button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Additional Info */}
                  {patient.triageNotes && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700">
                        <strong>Triage Notes:</strong> {patient.triageNotes}
                      </p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
