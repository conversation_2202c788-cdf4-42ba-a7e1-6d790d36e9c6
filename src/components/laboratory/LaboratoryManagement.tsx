import React, { useState, useEffect } from 'react';
import {
  TestTube,
  Search,
  Plus,
  Eye,
  Download,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Filter,
  Calendar,
  User,
  FileText,
  Printer,
  Send,
  RefreshCw,
  Beaker,
  Activity,
  TrendingUp
} from 'lucide-react';

interface LabTest {
  _id: string;
  testId: string;
  patientId: string;
  patientName: string;
  doctorId: string;
  doctorName: string;
  testName: string;
  testCategory: string;
  testType: string;
  priority: 'routine' | 'urgent' | 'stat';
  status: 'ordered' | 'collected' | 'processing' | 'completed' | 'reported' | 'cancelled';
  orderDate: string;
  collectionDate?: string;
  reportDate?: string;
  expectedDate: string;
  results?: {
    values: Array<{
      parameter: string;
      value: string;
      unit: string;
      referenceRange: string;
      status: 'normal' | 'abnormal' | 'critical';
    }>;
    interpretation: string;
    technician: string;
    reviewedBy: string;
  };
  instructions: string;
  notes?: string;
  cost: number;
  department: string;
  createdAt: string;
  updatedAt: string;
}

interface LabStats {
  totalTests: number;
  pendingTests: number;
  completedToday: number;
  urgentTests: number;
  averageProcessingTime: number;
  criticalResults: number;
}

export function LaboratoryManagement() {
  const [tests, setTests] = useState<LabTest[]>([]);
  const [stats, setStats] = useState<LabStats>({
    totalTests: 0,
    pendingTests: 0,
    completedToday: 0,
    urgentTests: 0,
    averageProcessingTime: 0,
    criticalResults: 0
  });
  const [selectedTest, setSelectedTest] = useState<LabTest | null>(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showResultModal, setShowResultModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [filterCategory, setFilterCategory] = useState('all');

  const testCategories = [
    'Hematology', 'Chemistry', 'Microbiology', 'Immunology',
    'Pathology', 'Radiology', 'Cardiology', 'Endocrinology'
  ];

  const commonTests = [
    { name: 'Complete Blood Count (CBC)', category: 'Hematology', cost: 45 },
    { name: 'Basic Metabolic Panel', category: 'Chemistry', cost: 65 },
    { name: 'Lipid Panel', category: 'Chemistry', cost: 55 },
    { name: 'Liver Function Tests', category: 'Chemistry', cost: 75 },
    { name: 'Thyroid Function Tests', category: 'Endocrinology', cost: 85 },
    { name: 'Urinalysis', category: 'Chemistry', cost: 35 },
    { name: 'Blood Culture', category: 'Microbiology', cost: 95 },
    { name: 'Chest X-Ray', category: 'Radiology', cost: 125 },
    { name: 'ECG', category: 'Cardiology', cost: 75 },
    { name: 'HbA1c', category: 'Endocrinology', cost: 65 }
  ];

  const statusOptions = [
    'ordered', 'collected', 'processing', 'completed', 'reported', 'cancelled'
  ];

  const priorityOptions = ['routine', 'urgent', 'stat'];

  useEffect(() => {
    fetchLabTests();
    fetchLabStats();

    // Set up real-time updates
    const interval = setInterval(() => {
      fetchLabTests();
      fetchLabStats();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchLabTests = async () => {
    try {
      setLoading(true);

      // Try to fetch from API first
      try {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (filterStatus !== 'all') params.append('status', filterStatus);
        if (filterPriority !== 'all') params.append('priority', filterPriority);
        if (filterCategory !== 'all') params.append('category', filterCategory);

        const response = await fetch(`/api/laboratory/tests?${params}`);
        if (response.ok) {
          const data = await response.json();
          setTests(data.tests || []);
          return;
        }
      } catch (apiError) {
        console.warn('Laboratory API not available, using fallback data');
      }

      // Fallback to mock data
      const mockTests: LabTest[] = [
        {
          _id: '1',
          testId: 'LAB001234',
          patientId: 'PT001234',
          patientName: 'John Doe',
          doctorId: 'DR001',
          doctorName: 'Dr. Sarah Johnson',
          testName: 'Complete Blood Count (CBC)',
          testCategory: 'Hematology',
          testType: 'Blood Test',
          priority: 'routine',
          status: 'completed',
          orderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          collectionDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          reportDate: new Date().toISOString(),
          expectedDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
          results: {
            values: [
              { parameter: 'Hemoglobin', value: '14.2', unit: 'g/dL', referenceRange: '12.0-15.5', status: 'normal' },
              { parameter: 'White Blood Cells', value: '7.8', unit: '10³/μL', referenceRange: '4.5-11.0', status: 'normal' },
              { parameter: 'Platelets', value: '285', unit: '10³/μL', referenceRange: '150-450', status: 'normal' },
              { parameter: 'Hematocrit', value: '42.1', unit: '%', referenceRange: '36.0-46.0', status: 'normal' }
            ],
            interpretation: 'All values within normal limits. No abnormalities detected.',
            technician: 'Lab Tech Mike',
            reviewedBy: 'Dr. Lab Director'
          },
          instructions: 'Fasting not required',
          cost: 45,
          department: 'Laboratory',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: '2',
          testId: 'LAB001235',
          patientId: 'PT001235',
          patientName: 'Jane Smith',
          doctorId: 'DR002',
          doctorName: 'Dr. Michael Brown',
          testName: 'Basic Metabolic Panel',
          testCategory: 'Chemistry',
          testType: 'Blood Test',
          priority: 'urgent',
          status: 'processing',
          orderDate: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          collectionDate: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          expectedDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
          instructions: 'Fasting required - 8 hours',
          cost: 65,
          department: 'Laboratory',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: '3',
          testId: 'LAB001236',
          patientId: 'PT001236',
          patientName: 'Robert Johnson',
          doctorId: 'DR001',
          doctorName: 'Dr. Sarah Johnson',
          testName: 'Lipid Panel',
          testCategory: 'Chemistry',
          testType: 'Blood Test',
          priority: 'stat',
          status: 'ordered',
          orderDate: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          expectedDate: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
          instructions: 'Fasting required - 12 hours',
          cost: 55,
          department: 'Laboratory',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      setTests(mockTests);
    } catch (err) {
      setError('Failed to fetch lab tests');
      console.error('Error fetching lab tests:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchLabStats = async () => {
    try {
      // Try to fetch from API first
      try {
        const response = await fetch('/api/laboratory/stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data.stats);
          return;
        }
      } catch (apiError) {
        console.warn('Lab stats API not available, calculating from test data');
      }

      // Calculate stats from test data
      const totalTests = tests.length;
      const pendingTests = tests.filter(t => ['ordered', 'collected', 'processing'].includes(t.status)).length;
      const today = new Date().toDateString();
      const completedToday = tests.filter(t =>
        t.status === 'completed' && new Date(t.reportDate || '').toDateString() === today
      ).length;
      const urgentTests = tests.filter(t => t.priority === 'urgent' || t.priority === 'stat').length;
      const criticalResults = tests.filter(t =>
        t.results?.values.some(v => v.status === 'critical')
      ).length;

      // Calculate average processing time (mock calculation)
      const averageProcessingTime = 24; // hours

      setStats({
        totalTests,
        pendingTests,
        completedToday,
        urgentTests,
        averageProcessingTime,
        criticalResults
      });
    } catch (err) {
      console.error('Error calculating lab stats:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ordered':
        return 'bg-blue-100 text-blue-800';
      case 'collected':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'reported':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'stat':
        return 'bg-red-100 text-red-800';
      case 'urgent':
        return 'bg-orange-100 text-orange-800';
      case 'routine':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ordered':
        return <Clock size={14} className="text-blue-600" />;
      case 'collected':
        return <TestTube size={14} className="text-yellow-600" />;
      case 'processing':
        return <Beaker size={14} className="text-purple-600" />;
      case 'completed':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'reported':
        return <FileText size={14} className="text-gray-600" />;
      case 'cancelled':
        return <XCircle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const updateTestStatus = async (testId: string, newStatus: string) => {
    try {
      // Update test status - replace with actual API call
      setTests(prev => prev.map(test =>
        test._id === testId
          ? {
              ...test,
              status: newStatus as any,
              updatedAt: new Date().toISOString(),
              ...(newStatus === 'collected' && { collectionDate: new Date().toISOString() }),
              ...(newStatus === 'completed' && { reportDate: new Date().toISOString() })
            }
          : test
      ));

      // Refresh stats
      fetchLabStats();
    } catch (err) {
      console.error('Error updating test status:', err);
    }
  };

  const filteredTests = tests.filter(test => {
    const matchesSearch = searchTerm === '' ||
      test.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      test.testName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      test.testId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      test.patientId.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'all' || test.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || test.priority === filterPriority;
    const matchesCategory = filterCategory === 'all' || test.testCategory === filterCategory;

    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  // Sort tests by priority and order date
  const sortedTests = filteredTests.sort((a, b) => {
    const priorityOrder = { 'stat': 0, 'urgent': 1, 'routine': 2 };
    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    }
    return new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime();
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Laboratory Management</h1>
          <p className="text-gray-600 mt-1">Manage lab tests, results, and reporting</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchLabTests}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
          >
            <RefreshCw size={20} />
            <span>Refresh</span>
          </button>
          <button
            onClick={() => setShowOrderModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus size={20} />
            <span>Order Test</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Tests</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalTests}</p>
            </div>
            <TestTube className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingTests}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Today</p>
              <p className="text-2xl font-bold text-green-600">{stats.completedToday}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Urgent</p>
              <p className="text-2xl font-bold text-red-600">{stats.urgentTests}</p>
            </div>
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Time</p>
              <p className="text-2xl font-bold text-blue-600">{stats.averageProcessingTime}h</p>
            </div>
            <Activity className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical</p>
              <p className="text-2xl font-bold text-red-600">{stats.criticalResults}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-red-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search by patient, test name, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              {statusOptions.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Priorities</option>
              {priorityOptions.map(priority => (
                <option key={priority} value={priority}>
                  {priority.charAt(0).toUpperCase() + priority.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              {testCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Lab Tests List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Laboratory Tests</h3>
          <p className="text-sm text-gray-600 mt-1">Manage test orders, collection, and results</p>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : sortedTests.length === 0 ? (
          <div className="text-center py-8">
            <TestTube className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No lab tests found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No tests match the current filter criteria.
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {sortedTests.map((test) => (
              <div
                key={test._id}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <TestTube size={20} className="text-blue-600" />
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900">{test.testName}</h4>
                      <p className="text-sm text-gray-600">
                        Patient: {test.patientName} • ID: {test.testId}
                      </p>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-xs text-gray-500">
                          <Calendar size={12} className="inline mr-1" />
                          Ordered: {new Date(test.orderDate).toLocaleDateString()}
                        </span>
                        <span className="text-xs text-gray-500">
                          <User size={12} className="inline mr-1" />
                          Dr: {test.doctorName}
                        </span>
                        <span className="text-xs text-gray-500">
                          Category: {test.testCategory}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    {/* Priority */}
                    <div className="text-center">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(test.priority)}`}>
                        {test.priority.toUpperCase()}
                      </span>
                    </div>

                    {/* Status */}
                    <div className="text-center">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(test.status)}`}>
                        {getStatusIcon(test.status)}
                        <span className="ml-1">{test.status.charAt(0).toUpperCase() + test.status.slice(1)}</span>
                      </span>
                    </div>

                    {/* Cost */}
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-700">Cost</p>
                      <p className="text-lg font-bold text-green-600">${test.cost}</p>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedTest(test)}
                        className="text-blue-600 hover:text-blue-800 p-2"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>

                      {test.status === 'ordered' && (
                        <button
                          onClick={() => updateTestStatus(test._id, 'collected')}
                          className="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700"
                        >
                          Collect
                        </button>
                      )}

                      {test.status === 'collected' && (
                        <button
                          onClick={() => updateTestStatus(test._id, 'processing')}
                          className="bg-purple-600 text-white px-3 py-1 rounded text-xs hover:bg-purple-700"
                        >
                          Process
                        </button>
                      )}

                      {test.status === 'processing' && (
                        <button
                          onClick={() => setShowResultModal(true)}
                          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                        >
                          Add Results
                        </button>
                      )}

                      {test.status === 'completed' && test.results && (
                        <button
                          className="text-green-600 hover:text-green-800 p-2"
                          title="Download Report"
                        >
                          <Download size={16} />
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Additional Info */}
                {test.instructions && (
                  <div className="mt-3 p-3 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      <strong>Instructions:</strong> {test.instructions}
                    </p>
                  </div>
                )}

                {/* Results Preview */}
                {test.results && (
                  <div className="mt-3 p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800 font-medium mb-2">Results Available</p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      {test.results.values.slice(0, 4).map((result, index) => (
                        <div key={index} className="text-green-700">
                          <span className="font-medium">{result.parameter}:</span> {result.value} {result.unit}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}