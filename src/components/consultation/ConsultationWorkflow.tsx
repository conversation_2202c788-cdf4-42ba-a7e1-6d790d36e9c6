import React, { useState, useEffect } from 'react';
import { 
  User, 
  Heart, 
  Thermometer, 
  Activity, 
  Stethoscope, 
  FileText, 
  Pill, 
  Calendar, 
  Save, 
  Send, 
  Plus, 
  Minus,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye,
  Printer,
  Download
} from 'lucide-react';

interface VitalSigns {
  temperature: number;
  bloodPressure: { systolic: number; diastolic: number };
  heartRate: number;
  respiratoryRate: number;
  oxygenSaturation: number;
  weight: number;
  height: number;
  bmi: number;
  painScale: number;
  bloodGlucose?: number;
}

interface Symptom {
  symptom: string;
  severity: 'mild' | 'moderate' | 'severe';
  duration: string;
  onset: 'sudden' | 'gradual';
  notes: string;
}

interface Diagnosis {
  code: string;
  description: string;
  type: 'primary' | 'secondary' | 'differential';
  confidence: 'confirmed' | 'probable' | 'possible';
}

interface Prescription {
  medicationName: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  quantity: number;
  refills: number;
}

interface ConsultationData {
  patientId: string;
  doctorId: string;
  consultationDate: string;
  consultationType: string;
  chiefComplaint: string;
  historyOfPresentIllness: string;
  vitalSigns: VitalSigns;
  symptoms: Symptom[];
  physicalExamination: {
    general: string;
    cardiovascular: string;
    respiratory: string;
    neurological: string;
    gastrointestinal: string;
    musculoskeletal: string;
    skin: string;
    other: string;
  };
  diagnoses: Diagnosis[];
  prescriptions: Prescription[];
  labTests: Array<{
    testName: string;
    urgency: 'routine' | 'urgent' | 'stat';
    instructions: string;
  }>;
  followUp: {
    required: boolean;
    timeframe: string;
    department: string;
    instructions: string;
  };
  consultationNotes: string;
  status: 'in-progress' | 'completed' | 'pending-review';
}

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  phone: string;
  email: string;
  medicalHistory: {
    allergies: Array<{ allergen: string; reaction: string; severity: string }>;
    chronicConditions: Array<{ condition: string; status: string }>;
    medications: Array<{ name: string; dosage: string; frequency: string }>;
  };
}

export function ConsultationWorkflow() {
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [consultationData, setConsultationData] = useState<ConsultationData>({
    patientId: '',
    doctorId: '',
    consultationDate: new Date().toISOString(),
    consultationType: 'routine',
    chiefComplaint: '',
    historyOfPresentIllness: '',
    vitalSigns: {
      temperature: 98.6,
      bloodPressure: { systolic: 120, diastolic: 80 },
      heartRate: 72,
      respiratoryRate: 16,
      oxygenSaturation: 98,
      weight: 0,
      height: 0,
      bmi: 0,
      painScale: 0
    },
    symptoms: [],
    physicalExamination: {
      general: '',
      cardiovascular: '',
      respiratory: '',
      neurological: '',
      gastrointestinal: '',
      musculoskeletal: '',
      skin: '',
      other: ''
    },
    diagnoses: [],
    prescriptions: [],
    labTests: [],
    followUp: {
      required: false,
      timeframe: '',
      department: '',
      instructions: ''
    },
    consultationNotes: '',
    status: 'in-progress'
  });
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);

  const consultationSteps = [
    { id: 1, title: 'Patient Selection', icon: User },
    { id: 2, title: 'Chief Complaint & History', icon: FileText },
    { id: 3, title: 'Vital Signs', icon: Activity },
    { id: 4, title: 'Symptoms Assessment', icon: AlertCircle },
    { id: 5, title: 'Physical Examination', icon: Stethoscope },
    { id: 6, title: 'Diagnosis', icon: CheckCircle },
    { id: 7, title: 'Prescriptions', icon: Pill },
    { id: 8, title: 'Follow-up & Notes', icon: Calendar }
  ];

  const consultationTypes = [
    'Routine Check-up', 'Follow-up', 'Emergency', 'Consultation', 
    'Procedure', 'Vaccination', 'Screening'
  ];

  const commonDiagnoses = [
    { code: 'Z00.00', description: 'Encounter for general adult medical examination without abnormal findings' },
    { code: 'I10', description: 'Essential hypertension' },
    { code: 'E11.9', description: 'Type 2 diabetes mellitus without complications' },
    { code: 'J06.9', description: 'Acute upper respiratory infection, unspecified' },
    { code: 'M79.3', description: 'Panniculitis, unspecified' },
    { code: 'R50.9', description: 'Fever, unspecified' },
    { code: 'R06.02', description: 'Shortness of breath' },
    { code: 'R51', description: 'Headache' }
  ];

  const commonMedications = [
    'Lisinopril', 'Metformin', 'Amlodipine', 'Atorvastatin', 'Omeprazole',
    'Levothyroxine', 'Albuterol', 'Ibuprofen', 'Acetaminophen', 'Amoxicillin'
  ];

  useEffect(() => {
    fetchPatients();
  }, []);

  useEffect(() => {
    if (consultationData.vitalSigns.weight && consultationData.vitalSigns.height) {
      const weightKg = consultationData.vitalSigns.weight * 0.453592;
      const heightM = consultationData.vitalSigns.height * 0.0254;
      const bmi = weightKg / (heightM * heightM);
      
      setConsultationData(prev => ({
        ...prev,
        vitalSigns: {
          ...prev.vitalSigns,
          bmi: Math.round(bmi * 10) / 10
        }
      }));
    }
  }, [consultationData.vitalSigns.weight, consultationData.vitalSigns.height]);

  const fetchPatients = async () => {
    try {
      // Try to fetch from API first
      try {
        const response = await fetch('/api/patients?limit=100');
        if (response.ok) {
          const data = await response.json();
          setPatients(data.patients || []);
          return;
        }
      } catch (apiError) {
        console.warn('Patients API not available, using fallback data');
      }
      
      // Fallback to mock data
      const mockPatients: Patient[] = [
        {
          _id: '1',
          patientId: 'PT001234',
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1985-06-15',
          gender: 'Male',
          phone: '******-0123',
          email: '<EMAIL>',
          medicalHistory: {
            allergies: [{ allergen: 'Penicillin', reaction: 'Rash', severity: 'Moderate' }],
            chronicConditions: [{ condition: 'Hypertension', status: 'Controlled' }],
            medications: [{ name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' }]
          }
        }
      ];
      
      setPatients(mockPatients);
    } catch (err) {
      console.error('Error fetching patients:', err);
    }
  };

  const addSymptom = () => {
    setConsultationData(prev => ({
      ...prev,
      symptoms: [...prev.symptoms, {
        symptom: '',
        severity: 'mild',
        duration: '',
        onset: 'gradual',
        notes: ''
      }]
    }));
  };

  const removeSymptom = (index: number) => {
    setConsultationData(prev => ({
      ...prev,
      symptoms: prev.symptoms.filter((_, i) => i !== index)
    }));
  };

  const addDiagnosis = () => {
    setConsultationData(prev => ({
      ...prev,
      diagnoses: [...prev.diagnoses, {
        code: '',
        description: '',
        type: 'primary',
        confidence: 'probable'
      }]
    }));
  };

  const removeDiagnosis = (index: number) => {
    setConsultationData(prev => ({
      ...prev,
      diagnoses: prev.diagnoses.filter((_, i) => i !== index)
    }));
  };

  const addPrescription = () => {
    setConsultationData(prev => ({
      ...prev,
      prescriptions: [...prev.prescriptions, {
        medicationName: '',
        dosage: '',
        frequency: '',
        duration: '',
        instructions: '',
        quantity: 30,
        refills: 0
      }]
    }));
  };

  const removePrescription = (index: number) => {
    setConsultationData(prev => ({
      ...prev,
      prescriptions: prev.prescriptions.filter((_, i) => i !== index)
    }));
  };

  const saveConsultation = async () => {
    try {
      setLoading(true);
      
      // Validate required fields
      if (!selectedPatient) {
        setError('Please select a patient');
        return;
      }
      
      if (!consultationData.chiefComplaint.trim()) {
        setError('Chief complaint is required');
        return;
      }

      const consultationPayload = {
        ...consultationData,
        patientId: selectedPatient._id,
        doctorId: 'current-doctor-id' // Would come from auth context
      };

      // Try to save to API
      try {
        const response = await fetch('/api/consultations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(consultationPayload)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('Consultation saved:', result);
          setError(null);
          // Reset form or redirect
          return;
        }
      } catch (apiError) {
        console.warn('Consultation API not available, saving locally');
      }
      
      // Fallback: save to localStorage
      const savedConsultations = JSON.parse(localStorage.getItem('consultations') || '[]');
      const newConsultation = {
        ...consultationPayload,
        _id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      
      savedConsultations.push(newConsultation);
      localStorage.setItem('consultations', JSON.stringify(savedConsultations));
      
      setError(null);
      alert('Consultation saved successfully!');
      
    } catch (err) {
      setError('Failed to save consultation');
      console.error('Error saving consultation:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Select Patient</h3>
            
            <div className="grid grid-cols-1 gap-4">
              {patients.map((patient) => (
                <div
                  key={patient._id}
                  onClick={() => setSelectedPatient(patient)}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedPatient?._id === patient._id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {patient.firstName} {patient.lastName}
                      </h4>
                      <p className="text-sm text-gray-600">
                        ID: {patient.patientId} • DOB: {new Date(patient.dateOfBirth).toLocaleDateString()}
                      </p>
                      <p className="text-sm text-gray-600">
                        {patient.gender} • {patient.phone}
                      </p>
                    </div>
                    {selectedPatient?._id === patient._id && (
                      <CheckCircle className="h-6 w-6 text-blue-600" />
                    )}
                  </div>
                  
                  {/* Medical History Summary */}
                  {patient.medicalHistory && (
                    <div className="mt-3 text-sm text-gray-600">
                      {patient.medicalHistory.allergies.length > 0 && (
                        <p><strong>Allergies:</strong> {patient.medicalHistory.allergies.map(a => a.allergen).join(', ')}</p>
                      )}
                      {patient.medicalHistory.chronicConditions.length > 0 && (
                        <p><strong>Conditions:</strong> {patient.medicalHistory.chronicConditions.map(c => c.condition).join(', ')}</p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Chief Complaint & History</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Consultation Type
              </label>
              <select
                value={consultationData.consultationType}
                onChange={(e) => setConsultationData(prev => ({ ...prev, consultationType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {consultationTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Chief Complaint *
              </label>
              <textarea
                value={consultationData.chiefComplaint}
                onChange={(e) => setConsultationData(prev => ({ ...prev, chiefComplaint: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Patient's main concern or reason for visit..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                History of Present Illness
              </label>
              <textarea
                value={consultationData.historyOfPresentIllness}
                onChange={(e) => setConsultationData(prev => ({ ...prev, historyOfPresentIllness: e.target.value }))}
                rows={5}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Detailed description of the current illness, including onset, duration, severity, associated symptoms..."
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Vital Signs</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Thermometer size={16} className="inline mr-1" />
                  Temperature (°F)
                </label>
                <input
                  type="number"
                  step="0.1"
                  value={consultationData.vitalSigns.temperature}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, temperature: parseFloat(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Heart size={16} className="inline mr-1" />
                  Heart Rate (bpm)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.heartRate}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, heartRate: parseInt(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Activity size={16} className="inline mr-1" />
                  Respiratory Rate
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.respiratoryRate}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, respiratoryRate: parseInt(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Blood Pressure (Systolic)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.bloodPressure.systolic}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: {
                      ...prev.vitalSigns,
                      bloodPressure: { ...prev.vitalSigns.bloodPressure, systolic: parseInt(e.target.value) || 0 }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Blood Pressure (Diastolic)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.bloodPressure.diastolic}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: {
                      ...prev.vitalSigns,
                      bloodPressure: { ...prev.vitalSigns.bloodPressure, diastolic: parseInt(e.target.value) || 0 }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Oxygen Saturation (%)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.oxygenSaturation}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, oxygenSaturation: parseInt(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Weight (lbs)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.weight}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, weight: parseFloat(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height (inches)
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.height}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, height: parseFloat(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  BMI
                </label>
                <input
                  type="number"
                  value={consultationData.vitalSigns.bmi}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pain Scale (0-10)
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  value={consultationData.vitalSigns.painScale}
                  onChange={(e) => setConsultationData(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, painScale: parseInt(e.target.value) || 0 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        );

      // Add cases for steps 4-8...
      default:
        return <div>Step content for step {currentStep}</div>;
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Consultation Workflow</h1>
          <p className="text-gray-600 mt-1">Complete patient consultation and medical documentation</p>
        </div>
        {selectedPatient && (
          <div className="text-right">
            <h3 className="font-semibold text-gray-900">
              {selectedPatient.firstName} {selectedPatient.lastName}
            </h3>
            <p className="text-sm text-gray-600">ID: {selectedPatient.patientId}</p>
          </div>
        )}
      </div>

      {/* Progress Steps */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between">
          {consultationSteps.map((step, index) => {
            const IconComponent = step.icon;
            const isActive = step.id === currentStep;
            const isCompleted = step.id < currentStep;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  isCompleted ? 'bg-green-100 text-green-600' :
                  isActive ? 'bg-blue-100 text-blue-600' :
                  'bg-gray-100 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircle size={20} />
                  ) : (
                    <IconComponent size={20} />
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-blue-600' : 'text-gray-600'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < consultationSteps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-300' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        {renderStepContent()}

        {/* Navigation */}
        <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
          <button
            onClick={() => setCurrentStep(prev => Math.max(prev - 1, 1))}
            disabled={currentStep === 1}
            className="px-6 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          <div className="text-sm text-gray-500">
            Step {currentStep} of {consultationSteps.length}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={saveConsultation}
              disabled={loading}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              <Save size={16} />
              <span>Save Draft</span>
            </button>

            {currentStep < consultationSteps.length ? (
              <button
                onClick={() => setCurrentStep(prev => Math.min(prev + 1, consultationSteps.length))}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Next
              </button>
            ) : (
              <button
                onClick={() => {
                  setConsultationData(prev => ({ ...prev, status: 'completed' }));
                  saveConsultation();
                }}
                disabled={loading}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                <Send size={16} />
                <span>Complete Consultation</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
