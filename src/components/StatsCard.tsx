import React from 'react';
import { DivideIcon as LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: LucideIcon;
  color: 'blue' | 'green' | 'purple' | 'emerald' | 'red' | 'yellow';
}

const colorClasses = {
  blue: 'bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 text-blue-600 dark:text-blue-400 shadow-blue-100 dark:shadow-blue-900/20',
  green: 'bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 text-green-600 dark:text-green-400 shadow-green-100 dark:shadow-green-900/20',
  purple: 'bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 text-purple-600 dark:text-purple-400 shadow-purple-100 dark:shadow-purple-900/20',
  emerald: 'bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 text-emerald-600 dark:text-emerald-400 shadow-emerald-100 dark:shadow-emerald-900/20',
  red: 'bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30 text-red-600 dark:text-red-400 shadow-red-100 dark:shadow-red-900/20',
  yellow: 'bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30 text-yellow-600 dark:text-yellow-400 shadow-yellow-100 dark:shadow-yellow-900/20',
};

export function StatsCard({ title, value, change, changeType, icon: Icon, color }: StatsCardProps) {
  const TrendIcon = changeType === 'increase' ? TrendingUp : TrendingDown;
  const changeColor = changeType === 'increase' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-slate-100 dark:border-gray-700 hover:border-slate-200 dark:hover:border-gray-600 group">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-slate-600 dark:text-gray-400 mb-2 uppercase tracking-wide">{title}</h3>
          <div className="text-3xl font-bold text-slate-900 dark:text-gray-100 mb-3 group-hover:text-slate-800 dark:group-hover:text-gray-200 transition-colors">{value}</div>
          <div className={`flex items-center space-x-1 text-sm font-medium ${changeColor}`}>
            <TrendIcon size={16} />
            <span>{change} from last month</span>
          </div>
        </div>
        <div className={`p-4 rounded-xl shadow-lg ${colorClasses[color]} group-hover:scale-110 transition-transform duration-300`}>
          <Icon size={26} />
        </div>
      </div>
    </div>
  );
}