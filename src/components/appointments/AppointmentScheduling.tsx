import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Check<PERSON>ircle, 
  XCircle, 
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Stethoscope,
  Filter,
  Download,
  Loader
} from 'lucide-react';

interface Doctor {
  _id: string;
  firstName: string;
  lastName: string;
  department: string;
  specialization: string;
  email: string;
  phone: string;
  availability: Array<{
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    isAvailable: boolean;
  }>;
}

interface Patient {
  _id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
}

interface Appointment {
  _id: string;
  appointmentId: string;
  patient: Patient;
  doctor: Doctor;
  appointmentDate: string;
  appointmentTime: string;
  duration: number;
  appointmentType: string;
  department: string;
  reason: string;
  status: string;
  priority: string;
  notes?: string;
  reminderSent: boolean;
  createdAt: string;
  updatedAt: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
  appointmentId?: string;
}

export function AppointmentScheduling() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedDoctor, setSelectedDoctor] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewAppointmentModal, setShowNewAppointmentModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const appointmentTypes = [
    'Consultation', 'Follow-up', 'Routine Check-up', 'Emergency', 
    'Procedure', 'Vaccination', 'Screening', 'Surgery'
  ];

  const departments = [
    'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 
    'General Medicine', 'Emergency Medicine', 'Surgery', 'Psychiatry'
  ];

  const appointmentStatuses = [
    'Scheduled', 'Confirmed', 'In Progress', 'Completed', 
    'Cancelled', 'No Show', 'Rescheduled'
  ];

  useEffect(() => {
    fetchAppointments();
    fetchDoctors();
    fetchPatients();
  }, [selectedDate, selectedDoctor, selectedDepartment]);

  useEffect(() => {
    if (selectedDoctor && selectedDate) {
      generateTimeSlots();
    }
  }, [selectedDoctor, selectedDate, appointments]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params: any = {};
      if (selectedDate) params.date = selectedDate;
      if (selectedDoctor) params.doctorId = selectedDoctor;
      if (selectedDepartment) params.department = selectedDepartment;
      if (filterStatus !== 'all') params.status = filterStatus;

      // Try to fetch from API first
      try {
        const response = await fetch(`/api/appointments?${new URLSearchParams(params)}`);
        if (response.ok) {
          const data = await response.json();
          setAppointments(data.appointments || []);
          return;
        }
      } catch (apiError) {
        console.warn('API not available, using fallback data');
      }

      // Fallback to mock data if API is not available
      const mockAppointments: Appointment[] = [
        {
          _id: '1',
          appointmentId: 'APT001234',
          patient: {
            _id: 'p1',
            patientId: 'PT001234',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '******-0123',
            dateOfBirth: '1985-06-15'
          },
          doctor: {
            _id: 'd1',
            firstName: 'Dr. Sarah',
            lastName: 'Johnson',
            department: 'Cardiology',
            specialization: 'Interventional Cardiology',
            email: '<EMAIL>',
            phone: '******-0100',
            availability: []
          },
          appointmentDate: selectedDate,
          appointmentTime: '09:00',
          duration: 30,
          appointmentType: 'Consultation',
          department: 'Cardiology',
          reason: 'Chest pain evaluation',
          status: 'Scheduled',
          priority: 'Medium',
          notes: 'Patient reports intermittent chest pain',
          reminderSent: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: '2',
          appointmentId: 'APT001235',
          patient: {
            _id: 'p2',
            patientId: 'PT001235',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '******-0124',
            dateOfBirth: '1990-03-22'
          },
          doctor: {
            _id: 'd1',
            firstName: 'Dr. Sarah',
            lastName: 'Johnson',
            department: 'Cardiology',
            specialization: 'Interventional Cardiology',
            email: '<EMAIL>',
            phone: '******-0100',
            availability: []
          },
          appointmentDate: selectedDate,
          appointmentTime: '10:30',
          duration: 45,
          appointmentType: 'Follow-up',
          department: 'Cardiology',
          reason: 'Post-procedure follow-up',
          status: 'Confirmed',
          priority: 'High',
          reminderSent: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      setAppointments(mockAppointments);
    } catch (err) {
      setError('Failed to fetch appointments');
      console.error('Error fetching appointments:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchDoctors = async () => {
    try {
      // Try to fetch from API first
      try {
        const response = await fetch('/api/users?role=doctor');
        if (response.ok) {
          const data = await response.json();
          setDoctors(data.users || []);
          return;
        }
      } catch (apiError) {
        console.warn('Doctors API not available, using fallback data');
      }

      // Fallback to mock data if API is not available
      const mockDoctors: Doctor[] = [
        {
          _id: 'd1',
          firstName: 'Dr. Sarah',
          lastName: 'Johnson',
          department: 'Cardiology',
          specialization: 'Interventional Cardiology',
          email: '<EMAIL>',
          phone: '******-0100',
          availability: [
            { dayOfWeek: 1, startTime: '08:00', endTime: '17:00', isAvailable: true },
            { dayOfWeek: 2, startTime: '08:00', endTime: '17:00', isAvailable: true },
            { dayOfWeek: 3, startTime: '08:00', endTime: '17:00', isAvailable: true },
            { dayOfWeek: 4, startTime: '08:00', endTime: '17:00', isAvailable: true },
            { dayOfWeek: 5, startTime: '08:00', endTime: '17:00', isAvailable: true }
          ]
        },
        {
          _id: 'd2',
          firstName: 'Dr. Michael',
          lastName: 'Brown',
          department: 'General Medicine',
          specialization: 'Internal Medicine',
          email: '<EMAIL>',
          phone: '******-0101',
          availability: [
            { dayOfWeek: 1, startTime: '09:00', endTime: '18:00', isAvailable: true },
            { dayOfWeek: 2, startTime: '09:00', endTime: '18:00', isAvailable: true },
            { dayOfWeek: 3, startTime: '09:00', endTime: '18:00', isAvailable: true },
            { dayOfWeek: 4, startTime: '09:00', endTime: '18:00', isAvailable: true },
            { dayOfWeek: 5, startTime: '09:00', endTime: '18:00', isAvailable: true }
          ]
        }
      ];
      setDoctors(mockDoctors);
    } catch (err) {
      console.error('Error fetching doctors:', err);
    }
  };

  const fetchPatients = async () => {
    try {
      // Try to fetch from API first
      try {
        const response = await fetch('/api/patients?limit=100');
        if (response.ok) {
          const data = await response.json();
          setPatients(data.patients || []);
          return;
        }
      } catch (apiError) {
        console.warn('Patients API not available, using fallback data');
      }

      // Fallback to mock data if API is not available
      const mockPatients: Patient[] = [
        {
          _id: 'p1',
          patientId: 'PT001234',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          dateOfBirth: '1985-06-15'
        },
        {
          _id: 'p2',
          patientId: 'PT001235',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '******-0124',
          dateOfBirth: '1990-03-22'
        }
      ];
      setPatients(mockPatients);
    } catch (err) {
      console.error('Error fetching patients:', err);
    }
  };

  const generateTimeSlots = () => {
    const doctor = doctors.find(d => d._id === selectedDoctor);
    if (!doctor) return;

    const selectedDateObj = new Date(selectedDate);
    const dayOfWeek = selectedDateObj.getDay();
    
    const availability = doctor.availability.find(a => a.dayOfWeek === dayOfWeek);
    if (!availability || !availability.isAvailable) {
      setTimeSlots([]);
      return;
    }

    const slots: TimeSlot[] = [];
    const startTime = new Date(`${selectedDate}T${availability.startTime}`);
    const endTime = new Date(`${selectedDate}T${availability.endTime}`);
    
    const current = new Date(startTime);
    while (current < endTime) {
      const timeString = current.toTimeString().slice(0, 5);
      
      // Check if this time slot is already booked
      const existingAppointment = appointments.find(apt => 
        apt.doctor._id === selectedDoctor && 
        apt.appointmentDate === selectedDate && 
        apt.appointmentTime === timeString &&
        apt.status !== 'Cancelled'
      );

      slots.push({
        time: timeString,
        available: !existingAppointment,
        appointmentId: existingAppointment?._id
      });

      current.setMinutes(current.getMinutes() + 30); // 30-minute slots
    }

    setTimeSlots(slots);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'Confirmed':
        return 'bg-green-100 text-green-800';
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'Completed':
        return 'bg-gray-100 text-gray-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      case 'No Show':
        return 'bg-orange-100 text-orange-800';
      case 'Rescheduled':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Scheduled':
        return <Clock size={14} className="text-blue-600" />;
      case 'Confirmed':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'In Progress':
        return <Stethoscope size={14} className="text-yellow-600" />;
      case 'Completed':
        return <CheckCircle size={14} className="text-gray-600" />;
      case 'Cancelled':
        return <XCircle size={14} className="text-red-600" />;
      case 'No Show':
        return <AlertCircle size={14} className="text-orange-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Urgent':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const updateAppointmentStatus = async (appointmentId: string, newStatus: string) => {
    try {
      // Update appointment status - replace with actual API call
      setAppointments(prev => prev.map(apt => 
        apt._id === appointmentId 
          ? { ...apt, status: newStatus, updatedAt: new Date().toISOString() }
          : apt
      ));
    } catch (err) {
      console.error('Error updating appointment status:', err);
    }
  };

  const filteredAppointments = appointments.filter(appointment => {
    const matchesSearch = searchTerm === '' || 
      appointment.patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.patient.patientId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctor.lastName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Appointment Scheduling</h1>
          <p className="text-gray-600 mt-1">Manage patient appointments and doctor schedules</p>
        </div>
        <button
          onClick={() => setShowNewAppointmentModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>New Appointment</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Departments</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Doctor</label>
            <select
              value={selectedDoctor}
              onChange={(e) => setSelectedDoctor(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Doctors</option>
              {doctors
                .filter(doctor => !selectedDepartment || doctor.department === selectedDepartment)
                .map(doctor => (
                  <option key={doctor._id} value={doctor._id}>
                    {doctor.firstName} {doctor.lastName} - {doctor.department}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              {appointmentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search by patient name, ID, or doctor..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Time Slots */}
        {selectedDoctor && (
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Available Time Slots - {selectedDate}
            </h3>
            
            {timeSlots.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No availability for selected date
              </p>
            ) : (
              <div className="grid grid-cols-2 gap-2">
                {timeSlots.map((slot, index) => (
                  <button
                    key={index}
                    disabled={!slot.available}
                    className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                      slot.available
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : 'bg-red-100 text-red-800 cursor-not-allowed'
                    }`}
                  >
                    {slot.time}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Appointments List */}
        <div className={`bg-white p-6 rounded-lg shadow-sm border border-gray-200 ${selectedDoctor ? 'lg:col-span-2' : 'lg:col-span-3'}`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Appointments for {new Date(selectedDate).toLocaleDateString()}
            </h3>
            <div className="flex items-center space-x-2">
              <button className="text-gray-400 hover:text-gray-600 p-2">
                <Filter size={16} />
              </button>
              <button className="text-gray-400 hover:text-gray-600 p-2">
                <Download size={16} />
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader className="animate-spin h-8 w-8 text-blue-500" />
            </div>
          ) : filteredAppointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No appointments found</h3>
              <p className="mt-1 text-sm text-gray-500">
                No appointments scheduled for the selected criteria.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAppointments.map((appointment) => (
                <div
                  key={appointment._id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <User size={20} className="text-blue-600" />
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {appointment.patient.firstName} {appointment.patient.lastName}
                        </h4>
                        <p className="text-sm text-gray-600">
                          ID: {appointment.patient.patientId}
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-xs text-gray-500 flex items-center">
                            <Clock size={12} className="mr-1" />
                            {appointment.appointmentTime}
                          </span>
                          <span className="text-xs text-gray-500 flex items-center">
                            <Stethoscope size={12} className="mr-1" />
                            {appointment.doctor.firstName} {appointment.doctor.lastName}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(appointment.priority)}`}>
                        {appointment.priority}
                      </span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(appointment.status)}`}>
                        {getStatusIcon(appointment.status)}
                        <span className="ml-1">{appointment.status}</span>
                      </span>
                    </div>
                  </div>

                  <div className="mt-3 text-sm text-gray-600">
                    <p><strong>Type:</strong> {appointment.appointmentType}</p>
                    <p><strong>Reason:</strong> {appointment.reason}</p>
                    {appointment.notes && (
                      <p><strong>Notes:</strong> {appointment.notes}</p>
                    )}
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Phone size={12} className="mr-1" />
                        {appointment.patient.phone}
                      </span>
                      <span className="flex items-center">
                        <Mail size={12} className="mr-1" />
                        {appointment.patient.email}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      {appointment.status === 'Scheduled' && (
                        <button
                          onClick={() => updateAppointmentStatus(appointment._id, 'Confirmed')}
                          className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                        >
                          Confirm
                        </button>
                      )}
                      {appointment.status === 'Confirmed' && (
                        <button
                          onClick={() => updateAppointmentStatus(appointment._id, 'In Progress')}
                          className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                        >
                          Start
                        </button>
                      )}
                      <button className="text-gray-400 hover:text-gray-600 p-1">
                        <Edit size={14} />
                      </button>
                      <button className="text-red-400 hover:text-red-600 p-1">
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
