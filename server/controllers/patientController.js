import mongoose from 'mongoose';

// Patient schema
const PatientSchema = new mongoose.Schema({
  patientId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'PT' + Date.now().toString().slice(-6);
    }
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  dateOfBirth: {
    type: Date,
    required: true
  },
  gender: {
    type: String,
    required: true,
    enum: ['Male', 'Female', 'Other']
  },
  email: {
    type: String,
    lowercase: true,
    trim: true,
    sparse: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true, default: 'USA' }
  },
  emergencyContact: {
    name: { type: String, required: true },
    relationship: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String }
  },
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String,
    expiryDate: Date
  },
  medicalHistory: {
    allergies: [String],
    chronicConditions: [String],
    medications: [String],
    surgeries: [{
      procedure: String,
      date: Date,
      hospital: String
    }]
  },
  bloodType: {
    type: String,
    enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Deceased'],
    default: 'Active'
  },
  admissionDate: Date,
  dischargeDate: Date,
  assignedDoctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  room: String,
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
PatientSchema.index({ patientId: 1 });
PatientSchema.index({ email: 1 });
PatientSchema.index({ phone: 1 });
PatientSchema.index({ firstName: 1, lastName: 1 });
PatientSchema.index({ assignedDoctor: 1 });

// Virtual for full name
PatientSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for age
PatientSchema.virtual('age').get(function() {
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Get existing model or create new one
let Patient;
try {
  Patient = mongoose.model('Patient');
} catch (error) {
  Patient = mongoose.model('Patient', PatientSchema);
}

// @desc    Get all patients with pagination and filtering
// @route   GET /api/patients
// @access  Private
export const getPatients = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = { isActive: true };
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.gender) {
      filter.gender = req.query.gender;
    }
    
    if (req.query.assignedDoctor) {
      filter.assignedDoctor = req.query.assignedDoctor;
    }

    // Search functionality
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { patientId: searchRegex },
        { email: searchRegex },
        { phone: searchRegex }
      ];
    }

    const patients = await Patient.find(filter)
      .populate('assignedDoctor', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-medicalHistory');

    const total = await Patient.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get patients error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patients'
    });
  }
};

// @desc    Get single patient
// @route   GET /api/patients/:id
// @access  Private
export const getPatient = async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id)
      .populate('assignedDoctor', 'firstName lastName email department');

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    res.status(200).json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Get patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient'
    });
  }
};

// @desc    Create new patient
// @route   POST /api/patients
// @access  Private
export const createPatient = async (req, res) => {
  try {
    // Check if patient with same email or phone already exists
    if (req.body.email) {
      const existingPatient = await Patient.findOne({ 
        email: req.body.email,
        isActive: true 
      });
      
      if (existingPatient) {
        return res.status(400).json({
          success: false,
          error: 'Patient with this email already exists'
        });
      }
    }

    const patient = await Patient.create(req.body);

    res.status(201).json({
      success: true,
      data: patient
    });
  } catch (error) {
    console.error('Create patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating patient'
    });
  }
};

// @desc    Update patient
// @route   PUT /api/patients/:id
// @access  Private
export const updatePatient = async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Check if email is being updated and if it already exists
    if (req.body.email && req.body.email !== patient.email) {
      const existingPatient = await Patient.findOne({ 
        email: req.body.email,
        _id: { $ne: req.params.id },
        isActive: true 
      });
      
      if (existingPatient) {
        return res.status(400).json({
          success: false,
          error: 'Patient with this email already exists'
        });
      }
    }

    const updatedPatient = await Patient.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('assignedDoctor', 'firstName lastName email department');

    res.status(200).json({
      success: true,
      data: updatedPatient
    });
  } catch (error) {
    console.error('Update patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating patient'
    });
  }
};

// @desc    Delete patient (soft delete)
// @route   DELETE /api/patients/:id
// @access  Private
export const deletePatient = async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json({
        success: false,
        error: 'Patient not found'
      });
    }

    // Soft delete by setting isActive to false
    patient.isActive = false;
    patient.status = 'Inactive';
    await patient.save();

    res.status(200).json({
      success: true,
      message: 'Patient deleted successfully'
    });
  } catch (error) {
    console.error('Delete patient error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting patient'
    });
  }
};

// @desc    Get patient statistics
// @route   GET /api/patients/stats
// @access  Private
export const getPatientStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    const [
      totalPatients,
      activePatients,
      newThisMonth,
      newThisWeek,
      malePatients,
      femalePatients,
      avgAge
    ] = await Promise.all([
      Patient.countDocuments({ isActive: true }),
      Patient.countDocuments({ isActive: true, status: 'Active' }),
      Patient.countDocuments({
        isActive: true,
        createdAt: { $gte: startOfMonth }
      }),
      Patient.countDocuments({
        isActive: true,
        createdAt: { $gte: startOfWeek }
      }),
      Patient.countDocuments({ isActive: true, gender: 'Male' }),
      Patient.countDocuments({ isActive: true, gender: 'Female' }),
      Patient.aggregate([
        { $match: { isActive: true } },
        {
          $addFields: {
            age: {
              $floor: {
                $divide: [
                  { $subtract: [new Date(), '$dateOfBirth'] },
                  365.25 * 24 * 60 * 60 * 1000
                ]
              }
            }
          }
        },
        { $group: { _id: null, avgAge: { $avg: '$age' } } }
      ])
    ]);

    const stats = {
      totalPatients,
      activePatients,
      newThisMonth,
      newThisWeek,
      genderDistribution: {
        male: malePatients,
        female: femalePatients
      },
      averageAge: avgAge.length > 0 ? Math.round(avgAge[0].avgAge) : 0
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get patient stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient statistics'
    });
  }
};
