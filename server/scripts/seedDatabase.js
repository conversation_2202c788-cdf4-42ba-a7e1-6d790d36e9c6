const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User.js');
const Patient = require('../models/Patient.js');
const Role = require('../models/Role.js');
const Permission = require('../models/Permission.js');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/hospital_management';

// Sample data generators
const generatePatientId = (index) => `PT${String(index).padStart(6, '0')}`;

// Medical conditions and symptoms
const medicalConditions = [
  'Hypertension', 'Diabetes Type 2', 'Asthma', 'Arthritis', 'Heart Disease',
  'Chronic Kidney Disease', 'COPD', 'Depression', 'Anxiety', 'Migraine',
  'Osteoporosis', 'Thyroid Disorder', 'High Cholesterol', 'Sleep Apnea'
];

const symptoms = [
  'Chest pain', 'Shortness of breath', 'Headache', 'Fatigue', 'Dizziness',
  'Nausea', 'Fever', 'Cough', 'Joint pain', 'Back pain', 'Abdominal pain',
  'Insomnia', 'Weight loss', 'Weight gain', 'Skin rash'
];

const medications = [
  { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' },
  { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' },
  { name: 'Albuterol', dosage: '90mcg', frequency: 'As needed' },
  { name: 'Ibuprofen', dosage: '400mg', frequency: 'Three times daily' },
  { name: 'Atorvastatin', dosage: '20mg', frequency: 'Once daily' },
  { name: 'Omeprazole', dosage: '20mg', frequency: 'Once daily' },
  { name: 'Amlodipine', dosage: '5mg', frequency: 'Once daily' },
  { name: 'Levothyroxine', dosage: '50mcg', frequency: 'Once daily' }
];

// Generate random data helpers
const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];
const getRandomDate = (start, end) => 
  new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
const getRandomNumber = (min, max) => 
  Math.floor(Math.random() * (max - min + 1)) + min;

async function clearDatabase() {
  console.log('🧹 Clearing existing data...');
  await Promise.all([
    Permission.deleteMany({}),
    Role.deleteMany({}),
    User.deleteMany({}),
    Patient.deleteMany({})
  ]);
}

async function seedPermissions() {
  console.log('🔐 Seeding permissions...');

  const permissions = [
    // Dashboard permissions
    { module: 'dashboard', action: 'view', resource: '*', description: 'View dashboard' },

    // Patient management permissions
    { module: 'patients', action: 'view', resource: '*', description: 'View patients' },
    { module: 'patients', action: 'create', resource: '*', description: 'Create new patients' },
    { module: 'patients', action: 'edit', resource: '*', description: 'Edit patient information' },
    { module: 'patients', action: 'delete', resource: '*', description: 'Delete patients' },

    // Clinical management permissions
    { module: 'clinical', action: 'view', resource: '*', description: 'View clinical data' },
    { module: 'clinical', action: 'create', resource: '*', description: 'Create clinical records' },
    { module: 'clinical', action: 'edit', resource: '*', description: 'Edit clinical records' },

    // Laboratory permissions
    { module: 'laboratory', action: 'view', resource: '*', description: 'View lab results' },
    { module: 'laboratory', action: 'create', resource: '*', description: 'Create lab orders' },
    { module: 'laboratory', action: 'edit', resource: '*', description: 'Edit lab orders' },

    // Pharmacy permissions
    { module: 'pharmacy', action: 'view', resource: '*', description: 'View pharmacy data' },
    { module: 'pharmacy', action: 'create', resource: '*', description: 'Create prescriptions' },
    { module: 'pharmacy', action: 'edit', resource: '*', description: 'Edit prescriptions' },

    // Financial permissions
    { module: 'financial', action: 'view', resource: '*', description: 'View financial data' },
    { module: 'financial', action: 'create', resource: '*', description: 'Create invoices' },
    { module: 'financial', action: 'edit', resource: '*', description: 'Edit financial records' },

    // HR permissions
    { module: 'hr', action: 'view', resource: '*', description: 'View HR data' },
    { module: 'hr', action: 'create', resource: '*', description: 'Create staff records' },
    { module: 'hr', action: 'edit', resource: '*', description: 'Edit staff records' },

    // Facility permissions
    { module: 'facility', action: 'view', resource: '*', description: 'View facility data' },
    { module: 'facility', action: 'create', resource: '*', description: 'Create facility bookings' },
    { module: 'facility', action: 'edit', resource: '*', description: 'Edit facility records' },

    // Admin permissions
    { module: 'admin', action: 'view', resource: '*', description: 'View admin panel' },
    { module: 'admin', action: 'view', resource: 'users', description: 'View users' },
    { module: 'admin', action: 'create', resource: 'users', description: 'Create users' },
    { module: 'admin', action: 'edit', resource: 'users', description: 'Edit users' },
    { module: 'admin', action: 'delete', resource: 'users', description: 'Delete users' },
    { module: 'admin', action: 'assign', resource: 'permissions', description: 'Assign permissions' },

    // Reports permissions
    { module: 'reports', action: 'view', resource: '*', description: 'View reports' },
    { module: 'reports', action: 'export', resource: '*', description: 'Export reports' }
  ];

  const createdPermissions = await Permission.insertMany(permissions);
  console.log(`✅ Created ${createdPermissions.length} permissions`);
  return createdPermissions;
}

async function seedRoles(permissions) {
  console.log('👑 Seeding roles...');

  const roles = [
    {
      name: 'Super Admin',
      description: 'Full system access',
      level: 10,
      isSystemRole: true,
      defaultPermissions: permissions.map(p => p._id)
    },
    {
      name: 'Hospital Administrator',
      description: 'Hospital management access',
      level: 9,
      isSystemRole: true,
      defaultPermissions: permissions.filter(p =>
        !p.resource.includes('users') || p.action === 'view'
      ).map(p => p._id)
    },
    {
      name: 'Doctor',
      description: 'Clinical and patient management access',
      level: 7,
      isSystemRole: true,
      defaultPermissions: permissions.filter(p =>
        ['dashboard', 'patients', 'clinical', 'laboratory', 'pharmacy', 'reports'].includes(p.module)
      ).map(p => p._id)
    },
    {
      name: 'Nurse',
      description: 'Patient care and clinical access',
      level: 5,
      isSystemRole: true,
      defaultPermissions: permissions.filter(p =>
        ['dashboard', 'patients', 'clinical', 'pharmacy'].includes(p.module) &&
        p.action !== 'delete'
      ).map(p => p._id)
    },
    {
      name: 'Receptionist',
      description: 'Patient registration and basic access',
      level: 2,
      isSystemRole: true,
      defaultPermissions: permissions.filter(p =>
        ['dashboard', 'patients'].includes(p.module) &&
        ['view', 'create', 'edit'].includes(p.action)
      ).map(p => p._id)
    }
  ];

  const createdRoles = await Role.insertMany(roles);
  console.log(`✅ Created ${createdRoles.length} roles`);
  return createdRoles;
}

async function seedUsers(roles) {
  console.log('👥 Seeding users...');

  const hashedPassword = await bcrypt.hash('password123', 12);

  // Find roles
  const superAdminRole = roles.find(r => r.name === 'Super Admin');
  const doctorRole = roles.find(r => r.name === 'Doctor');
  const nurseRole = roles.find(r => r.name === 'Nurse');
  const receptionistRole = roles.find(r => r.name === 'Receptionist');
  
  const usersData = [
    // Admin
    {
      username: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: superAdminRole._id,
      permissions: superAdminRole.defaultPermissions,
      department: 'Administration',
      isActive: true
    },
    // Doctors
    {
      username: 'sarah.johnson',
      firstName: 'Dr. Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      password: hashedPassword,
      role: doctorRole._id,
      permissions: doctorRole.defaultPermissions,
      department: 'Cardiology',
      specialization: 'Interventional Cardiology',
      licenseNumber: 'MD12345',
      isActive: true
    },
    {
      username: 'michael.brown',
      firstName: 'Dr. Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      password: hashedPassword,
      role: doctorRole._id,
      permissions: doctorRole.defaultPermissions,
      department: 'General Medicine',
      specialization: 'Internal Medicine',
      licenseNumber: 'MD12346',
      isActive: true
    },
    // Nurses
    {
      username: 'mary.wilson',
      firstName: 'Mary',
      lastName: 'Wilson',
      email: '<EMAIL>',
      password: hashedPassword,
      role: nurseRole._id,
      permissions: nurseRole.defaultPermissions,
      department: 'General Medicine',
      isActive: true
    },
    // Receptionists
    {
      username: 'lisa.anderson',
      firstName: 'Lisa',
      lastName: 'Anderson',
      email: '<EMAIL>',
      password: hashedPassword,
      role: receptionistRole._id,
      permissions: receptionistRole.defaultPermissions,
      department: 'Front Desk',
      isActive: true
    },
    // Receptionists
    {
      firstName: 'Lisa',
      lastName: 'Anderson',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'receptionist',
      department: 'Front Desk',
      isActive: true
    }
  ];

  const users = await User.insertMany(usersData);
  console.log(`✅ Created ${users.length} users`);
  return users;
}

async function seedPatients(users) {
  console.log('🏥 Seeding patients...');
  
  const firstNames = [
    'John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Mary',
    'James', 'Patricia', 'William', 'Jennifer', 'Richard', 'Elizabeth',
    'Charles', 'Linda', 'Joseph', 'Barbara', 'Thomas', 'Susan'
  ];
  
  const lastNames = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
    'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
    'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'
  ];

  const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
  const genders = ['Male', 'Female'];
  const insuranceProviders = [
    'Blue Cross Blue Shield', 'Aetna', 'Medicare', 'Medicaid', 'Cigna', 'UnitedHealth'
  ];

  const patientsData = [];
  
  for (let i = 1; i <= 50; i++) {
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    const dateOfBirth = getRandomDate(new Date('1940-01-01'), new Date('2010-01-01'));
    const gender = getRandomElement(genders);
    
    // Generate some patients with chronic conditions
    const hasChronicConditions = Math.random() < 0.3;
    const hasAllergies = Math.random() < 0.25;
    
    const patientData = {
      patientId: generatePatientId(i),
      firstName,
      lastName,
      middleName: Math.random() < 0.3 ? getRandomElement(['A', 'B', 'C', 'D', 'E']) : undefined,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      phone: `******-${String(getRandomNumber(1000, 9999))}`,
      alternatePhone: Math.random() < 0.4 ? `******-${String(getRandomNumber(1000, 9999))}` : undefined,
      dateOfBirth: dateOfBirth.toISOString(),
      gender,
      bloodType: getRandomElement(bloodTypes),
      address: {
        street: `${getRandomNumber(100, 9999)} ${getRandomElement(['Main', 'Oak', 'Pine', 'Elm', 'Cedar'])} St`,
        city: getRandomElement(['Springfield', 'Franklin', 'Georgetown', 'Madison', 'Riverside']),
        state: getRandomElement(['CA', 'NY', 'TX', 'FL', 'IL']),
        zipCode: String(getRandomNumber(10000, 99999)),
        country: 'USA'
      },
      emergencyContact: {
        name: `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}`,
        relationship: getRandomElement(['Spouse', 'Parent', 'Sibling', 'Child', 'Friend']),
        phone: `******-${String(getRandomNumber(1000, 9999))}`,
        email: `emergency${i}@email.com`
      },
      insurance: {
        provider: getRandomElement(insuranceProviders),
        policyNumber: `POL${String(getRandomNumber(100000, 999999))}`,
        groupNumber: `GRP${String(getRandomNumber(1000, 9999))}`,
        expiryDate: getRandomDate(new Date(), new Date('2025-12-31')).toISOString(),
        copayAmount: getRandomNumber(10, 50),
        deductibleAmount: getRandomNumber(500, 5000),
        coverageType: getRandomElement(['Individual', 'Family', 'Employee'])
      },
      medicalHistory: {
        allergies: hasAllergies ? [
          {
            allergen: getRandomElement(['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust']),
            reaction: getRandomElement(['Rash', 'Swelling', 'Difficulty breathing', 'Nausea']),
            severity: getRandomElement(['Mild', 'Moderate', 'Severe']),
            dateIdentified: getRandomDate(new Date('2010-01-01'), new Date()).toISOString()
          }
        ] : [],
        chronicConditions: hasChronicConditions ? [
          {
            condition: getRandomElement(medicalConditions),
            diagnosedDate: getRandomDate(new Date('2015-01-01'), new Date()).toISOString(),
            status: getRandomElement(['Active', 'Controlled', 'Resolved']),
            notes: 'Patient managing condition well with medication'
          }
        ] : [],
        medications: Math.random() < 0.4 ? [
          {
            ...getRandomElement(medications),
            startDate: getRandomDate(new Date('2020-01-01'), new Date()).toISOString(),
            prescribedBy: 'Dr. Johnson',
            status: getRandomElement(['Active', 'Discontinued', 'Completed'])
          }
        ] : [],
        surgeries: Math.random() < 0.2 ? [
          {
            procedure: getRandomElement(['Appendectomy', 'Gallbladder removal', 'Knee replacement']),
            date: getRandomDate(new Date('2010-01-01'), new Date()).toISOString(),
            hospital: 'General Hospital',
            surgeon: 'Dr. Smith',
            outcome: 'Successful recovery'
          }
        ] : [],
        familyHistory: Math.random() < 0.3 ? [
          {
            relationship: getRandomElement(['Father', 'Mother', 'Sibling']),
            condition: getRandomElement(medicalConditions),
            ageAtDiagnosis: getRandomNumber(30, 70),
            notes: 'Family history noted for screening purposes'
          }
        ] : []
      },
      status: getRandomElement(['Active', 'Inactive']),
      assignedDoctor: users.find(u => u.role === 'doctor')?._id,
      isActive: true,
      createdBy: users.find(u => u.role === 'admin')?._id
    };
    
    patientsData.push(patientData);
  }

  const patients = await Patient.insertMany(patientsData);
  console.log(`✅ Created ${patients.length} patients`);
  return patients;
}

async function seedDatabase() {
  try {
    console.log('🚀 Starting database seeding process...');
    console.log(`📡 Connecting to MongoDB: ${MONGODB_URI}`);
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');
    
    // Clear existing data
    await clearDatabase();
    
    // Seed in order
    const permissions = await seedPermissions();
    const roles = await seedRoles(permissions);
    const users = await seedUsers(roles);
    const patients = await seedPatients(users);

    console.log('🎉 Database seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Permissions: ${permissions.length} permissions created`);
    console.log(`   - Roles: ${roles.length} roles created`);
    console.log(`   - Users: ${users.length} (Admin, Doctors, Nurses, Receptionists)`);
    console.log(`   - Patients: ${patients.length} patients with complete profiles`);
    
  } catch (error) {
    console.error('❌ Error during database seeding:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Run the seeder
seedDatabase();
