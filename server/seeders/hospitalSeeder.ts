import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';
import Patient from '../models/Patient.js';
import PatientVisit from '../models/PatientVisit.js';
import TreatmentPlan from '../models/TreatmentPlan.js';
import PatientDocument from '../models/PatientDocument.js';
import VitalSigns from '../models/VitalSigns.js';

// Sample data generators
const generatePatientId = (index: number) => `PT${String(index).padStart(6, '0')}`;
const generateVisitId = (index: number) => `VIS${String(index).padStart(6, '0')}`;
const generateTreatmentPlanId = (index: number) => `TP${String(index).padStart(6, '0')}`;

// Medical conditions and symptoms
const medicalConditions = [
  'Hypertension', 'Diabetes Type 2', 'Asthma', 'Arthritis', 'Heart Disease',
  'Chronic Kidney Disease', 'COPD', 'Depression', 'Anxiety', 'Migraine',
  'Osteoporosis', 'Thyroid Disorder', 'High Cholesterol', 'Sleep Apnea'
];

const symptoms = [
  'Chest pain', 'Shortness of breath', 'Headache', 'Fatigue', 'Dizziness',
  'Nausea', 'Fever', 'Cough', 'Joint pain', 'Back pain', 'Abdominal pain',
  'Insomnia', 'Weight loss', 'Weight gain', 'Skin rash'
];

const medications = [
  { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' },
  { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' },
  { name: 'Albuterol', dosage: '90mcg', frequency: 'As needed' },
  { name: 'Ibuprofen', dosage: '400mg', frequency: 'Three times daily' },
  { name: 'Atorvastatin', dosage: '20mg', frequency: 'Once daily' },
  { name: 'Omeprazole', dosage: '20mg', frequency: 'Once daily' },
  { name: 'Amlodipine', dosage: '5mg', frequency: 'Once daily' },
  { name: 'Levothyroxine', dosage: '50mcg', frequency: 'Once daily' }
];

const departments = [
  'Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'General Medicine',
  'Emergency Medicine', 'Surgery', 'Psychiatry', 'Dermatology', 'Oncology'
];

const visitTypes = [
  'Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 
  'Procedure', 'Vaccination', 'Screening'
];

// Generate random data helpers
const getRandomElement = (array: any[]) => array[Math.floor(Math.random() * array.length)];
const getRandomDate = (start: Date, end: Date) => 
  new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
const getRandomNumber = (min: number, max: number) => 
  Math.floor(Math.random() * (max - min + 1)) + min;

export class HospitalSeeder {
  private users: any[] = [];
  private patients: any[] = [];

  async seedAll() {
    try {
      console.log('🌱 Starting hospital database seeding...');
      
      // Clear existing data
      await this.clearDatabase();
      
      // Seed in order
      await this.seedUsers();
      await this.seedPatients();
      await this.seedPatientVisits();
      await this.seedTreatmentPlans();
      await this.seedVitalSigns();
      await this.seedPatientDocuments();
      
      console.log('✅ Hospital database seeding completed successfully!');
    } catch (error) {
      console.error('❌ Error seeding database:', error);
      throw error;
    }
  }

  private async clearDatabase() {
    console.log('🧹 Clearing existing data...');
    await Promise.all([
      User.deleteMany({}),
      Patient.deleteMany({}),
      PatientVisit.deleteMany({}),
      TreatmentPlan.deleteMany({}),
      PatientDocument.deleteMany({}),
      VitalSigns.deleteMany({})
    ]);
  }

  private async seedUsers() {
    console.log('👥 Seeding users...');
    
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const usersData = [
      // Admin
      {
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        department: 'Administration',
        isActive: true
      },
      // Doctors
      {
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'doctor',
        department: 'Cardiology',
        specialization: 'Interventional Cardiology',
        licenseNumber: 'MD12345',
        isActive: true
      },
      {
        firstName: 'Dr. Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'doctor',
        department: 'General Medicine',
        specialization: 'Internal Medicine',
        licenseNumber: 'MD12346',
        isActive: true
      },
      {
        firstName: 'Dr. Emily',
        lastName: 'Davis',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'doctor',
        department: 'Pediatrics',
        specialization: 'Pediatric Medicine',
        licenseNumber: 'MD12347',
        isActive: true
      },
      // Nurses
      {
        firstName: 'Nurse Mary',
        lastName: 'Wilson',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'nurse',
        department: 'General Medicine',
        isActive: true
      },
      {
        firstName: 'Nurse John',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'nurse',
        department: 'Emergency Medicine',
        isActive: true
      },
      // Receptionists
      {
        firstName: 'Lisa',
        lastName: 'Anderson',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'receptionist',
        department: 'Front Desk',
        isActive: true
      }
    ];

    this.users = await User.insertMany(usersData);
    console.log(`✅ Created ${this.users.length} users`);
  }

  private async seedPatients() {
    console.log('🏥 Seeding patients...');
    
    const firstNames = [
      'John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Mary',
      'James', 'Patricia', 'William', 'Jennifer', 'Richard', 'Elizabeth',
      'Charles', 'Linda', 'Joseph', 'Barbara', 'Thomas', 'Susan'
    ];
    
    const lastNames = [
      'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
      'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
      'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'
    ];

    const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
    const genders = ['Male', 'Female'];
    const insuranceProviders = [
      'Blue Cross Blue Shield', 'Aetna', 'Medicare', 'Medicaid', 'Cigna', 'UnitedHealth'
    ];

    const patientsData = [];
    
    for (let i = 1; i <= 50; i++) {
      const firstName = getRandomElement(firstNames);
      const lastName = getRandomElement(lastNames);
      const dateOfBirth = getRandomDate(new Date('1940-01-01'), new Date('2010-01-01'));
      const gender = getRandomElement(genders);
      
      // Generate some patients with chronic conditions
      const hasChronicConditions = Math.random() < 0.3;
      const hasAllergies = Math.random() < 0.25;
      
      const patientData = {
        patientId: generatePatientId(i),
        firstName,
        lastName,
        middleName: Math.random() < 0.3 ? getRandomElement(['A', 'B', 'C', 'D', 'E']) : undefined,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
        phone: `******-${String(getRandomNumber(1000, 9999))}`,
        alternatePhone: Math.random() < 0.4 ? `******-${String(getRandomNumber(1000, 9999))}` : undefined,
        dateOfBirth: dateOfBirth.toISOString(),
        gender,
        bloodType: getRandomElement(bloodTypes),
        address: {
          street: `${getRandomNumber(100, 9999)} ${getRandomElement(['Main', 'Oak', 'Pine', 'Elm', 'Cedar'])} St`,
          city: getRandomElement(['Springfield', 'Franklin', 'Georgetown', 'Madison', 'Riverside']),
          state: getRandomElement(['CA', 'NY', 'TX', 'FL', 'IL']),
          zipCode: String(getRandomNumber(10000, 99999)),
          country: 'USA'
        },
        emergencyContact: {
          name: `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}`,
          relationship: getRandomElement(['Spouse', 'Parent', 'Sibling', 'Child', 'Friend']),
          phone: `******-${String(getRandomNumber(1000, 9999))}`,
          email: `emergency${i}@email.com`
        },
        insurance: {
          provider: getRandomElement(insuranceProviders),
          policyNumber: `POL${String(getRandomNumber(100000, 999999))}`,
          groupNumber: `GRP${String(getRandomNumber(1000, 9999))}`,
          expiryDate: getRandomDate(new Date(), new Date('2025-12-31')).toISOString(),
          copayAmount: getRandomNumber(10, 50),
          deductibleAmount: getRandomNumber(500, 5000),
          coverageType: getRandomElement(['Individual', 'Family', 'Employee'])
        },
        medicalHistory: {
          allergies: hasAllergies ? [
            {
              allergen: getRandomElement(['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust']),
              reaction: getRandomElement(['Rash', 'Swelling', 'Difficulty breathing', 'Nausea']),
              severity: getRandomElement(['Mild', 'Moderate', 'Severe']),
              dateIdentified: getRandomDate(new Date('2010-01-01'), new Date()).toISOString()
            }
          ] : [],
          chronicConditions: hasChronicConditions ? [
            {
              condition: getRandomElement(medicalConditions),
              diagnosedDate: getRandomDate(new Date('2015-01-01'), new Date()).toISOString(),
              status: getRandomElement(['Active', 'Controlled', 'Resolved']),
              notes: 'Patient managing condition well with medication'
            }
          ] : [],
          medications: Math.random() < 0.4 ? [
            {
              ...getRandomElement(medications),
              startDate: getRandomDate(new Date('2020-01-01'), new Date()).toISOString(),
              prescribedBy: 'Dr. Johnson',
              status: getRandomElement(['Active', 'Discontinued', 'Completed'])
            }
          ] : [],
          surgeries: Math.random() < 0.2 ? [
            {
              procedure: getRandomElement(['Appendectomy', 'Gallbladder removal', 'Knee replacement']),
              date: getRandomDate(new Date('2010-01-01'), new Date()).toISOString(),
              hospital: 'General Hospital',
              surgeon: 'Dr. Smith',
              outcome: 'Successful recovery'
            }
          ] : [],
          familyHistory: Math.random() < 0.3 ? [
            {
              relationship: getRandomElement(['Father', 'Mother', 'Sibling']),
              condition: getRandomElement(medicalConditions),
              ageAtDiagnosis: getRandomNumber(30, 70),
              notes: 'Family history noted for screening purposes'
            }
          ] : []
        },
        status: getRandomElement(['Active', 'Inactive']),
        assignedDoctor: this.users.find(u => u.role === 'doctor')?._id,
        isActive: true,
        createdBy: this.users.find(u => u.role === 'admin')?._id
      };
      
      patientsData.push(patientData);
    }

    this.patients = await Patient.insertMany(patientsData);
    console.log(`✅ Created ${this.patients.length} patients`);
  }

  private async seedPatientVisits() {
    console.log('🏥 Seeding patient visits...');
    
    const visitsData = [];
    const doctors = this.users.filter(u => u.role === 'doctor');
    
    // Create 2-5 visits per patient
    for (let i = 0; i < this.patients.length; i++) {
      const patient = this.patients[i];
      const numVisits = getRandomNumber(2, 5);
      
      for (let j = 1; j <= numVisits; j++) {
        const doctor = getRandomElement(doctors);
        const visitDate = getRandomDate(new Date('2023-01-01'), new Date());
        const visitType = getRandomElement(visitTypes);
        const department = doctor.department;
        
        const visitData = {
          visitId: generateVisitId(i * 10 + j),
          patient: patient._id,
          doctor: doctor._id,
          visitDate: visitDate.toISOString(),
          visitTime: `${getRandomNumber(8, 17)}:${getRandomElement(['00', '15', '30', '45'])}`,
          visitType,
          department,
          chiefComplaint: getRandomElement(symptoms),
          historyOfPresentIllness: `Patient presents with ${getRandomElement(symptoms).toLowerCase()} for the past ${getRandomNumber(1, 14)} days.`,
          physicalExamination: {
            vitalSigns: {
              temperature: +(96 + Math.random() * 6).toFixed(1),
              bloodPressure: {
                systolic: getRandomNumber(110, 160),
                diastolic: getRandomNumber(70, 100)
              },
              heartRate: getRandomNumber(60, 100),
              respiratoryRate: getRandomNumber(12, 20),
              oxygenSaturation: getRandomNumber(95, 100),
              weight: getRandomNumber(120, 250),
              height: getRandomNumber(60, 75),
              bmi: 0 // Will be calculated
            },
            generalAppearance: 'Patient appears comfortable and in no acute distress',
            systemsReview: {
              cardiovascular: 'Regular rate and rhythm, no murmurs',
              respiratory: 'Clear to auscultation bilaterally',
              neurological: 'Alert and oriented x3'
            }
          },
          assessment: {
            primaryDiagnosis: {
              code: `ICD${getRandomNumber(10, 99)}.${getRandomNumber(10, 99)}`,
              description: getRandomElement(medicalConditions)
            },
            secondaryDiagnoses: [],
            differentialDiagnoses: []
          },
          plan: {
            medications: Math.random() < 0.7 ? [
              {
                ...getRandomElement(medications),
                instructions: 'Take with food',
                duration: `${getRandomNumber(7, 30)} days`,
                prescribedBy: doctor._id
              }
            ] : [],
            procedures: [],
            followUp: {
              required: Math.random() < 0.6,
              timeframe: `${getRandomNumber(1, 4)} weeks`,
              department,
              instructions: 'Return if symptoms worsen'
            },
            labTests: Math.random() < 0.4 ? [
              {
                testName: getRandomElement(['CBC', 'Basic Metabolic Panel', 'Lipid Panel']),
                urgency: getRandomElement(['Routine', 'STAT', 'Urgent']),
                instructions: 'Fasting required'
              }
            ] : []
          },
          visitNotes: `Patient seen for ${visitType.toLowerCase()}. ${getRandomElement(symptoms)} addressed with appropriate treatment plan.`,
          visitDuration: getRandomNumber(15, 60),
          status: getRandomElement(['Scheduled', 'In Progress', 'Completed', 'Cancelled']),
          createdBy: doctor._id
        };
        
        // Calculate BMI
        if (visitData.physicalExamination.vitalSigns.weight && visitData.physicalExamination.vitalSigns.height) {
          const weightKg = visitData.physicalExamination.vitalSigns.weight * 0.453592;
          const heightM = visitData.physicalExamination.vitalSigns.height * 0.0254;
          visitData.physicalExamination.vitalSigns.bmi = +(weightKg / (heightM * heightM)).toFixed(1);
        }
        
        visitsData.push(visitData);
      }
    }

    await PatientVisit.insertMany(visitsData);
    console.log(`✅ Created ${visitsData.length} patient visits`);
  }

  private async seedTreatmentPlans() {
    console.log('📋 Seeding treatment plans...');
    
    const treatmentPlansData = [];
    const doctors = this.users.filter(u => u.role === 'doctor');
    
    // Create treatment plans for 30% of patients
    const patientsWithPlans = this.patients.slice(0, Math.floor(this.patients.length * 0.3));
    
    for (let i = 0; i < patientsWithPlans.length; i++) {
      const patient = patientsWithPlans[i];
      const doctor = getRandomElement(doctors);
      const condition = getRandomElement(medicalConditions);
      
      const planData = {
        planId: generateTreatmentPlanId(i + 1),
        patient: patient._id,
        doctor: doctor._id,
        planName: `${condition} Management Plan`,
        planType: getRandomElement(['Acute', 'Chronic', 'Preventive', 'Rehabilitation']),
        startDate: getRandomDate(new Date('2023-01-01'), new Date()).toISOString(),
        endDate: Math.random() < 0.3 ? getRandomDate(new Date(), new Date('2024-12-31')).toISOString() : undefined,
        priority: getRandomElement(['Low', 'Medium', 'High', 'Critical']),
        primaryCondition: {
          diagnosis: condition,
          severity: getRandomElement(['Mild', 'Moderate', 'Severe']),
          onsetDate: getRandomDate(new Date('2020-01-01'), new Date()).toISOString()
        },
        goals: [
          {
            goalId: `GL${String(i + 1).padStart(3, '0')}`,
            description: `Reduce symptoms of ${condition.toLowerCase()}`,
            targetDate: getRandomDate(new Date(), new Date('2024-06-30')).toISOString(),
            status: getRandomElement(['Not Started', 'In Progress', 'Achieved', 'Modified']),
            progress: getRandomNumber(0, 100),
            notes: 'Patient showing good progress'
          }
        ],
        medications: [
          {
            medicationId: `MD${String(i + 1).padStart(3, '0')}`,
            ...getRandomElement(medications),
            startDate: getRandomDate(new Date('2023-01-01'), new Date()).toISOString(),
            instructions: 'Take with food, avoid alcohol',
            prescribedBy: doctor._id,
            status: getRandomElement(['Active', 'Completed', 'Discontinued', 'On Hold'])
          }
        ],
        planNotes: `Comprehensive treatment plan for ${condition} management with regular monitoring and follow-up.`,
        status: getRandomElement(['Active', 'Completed', 'Discontinued', 'On Hold']),
        createdBy: doctor._id
      };
      
      treatmentPlansData.push(planData);
    }

    await TreatmentPlan.insertMany(treatmentPlansData);
    console.log(`✅ Created ${treatmentPlansData.length} treatment plans`);
  }

  private async seedVitalSigns() {
    console.log('📊 Seeding vital signs...');
    
    const vitalSignsData = [];
    
    // Create vital signs for each patient (multiple entries)
    for (const patient of this.patients) {
      const numEntries = getRandomNumber(3, 8);
      
      for (let i = 0; i < numEntries; i++) {
        const recordDate = getRandomDate(new Date('2023-01-01'), new Date());
        
        const vitalData = {
          patient: patient._id,
          recordedDate: recordDate.toISOString(),
          recordedTime: `${getRandomNumber(8, 17)}:${getRandomElement(['00', '15', '30', '45'])}`,
          temperature: +(96 + Math.random() * 6).toFixed(1),
          bloodPressure: {
            systolic: getRandomNumber(110, 160),
            diastolic: getRandomNumber(70, 100)
          },
          heartRate: getRandomNumber(60, 100),
          respiratoryRate: getRandomNumber(12, 20),
          oxygenSaturation: getRandomNumber(95, 100),
          weight: getRandomNumber(120, 250),
          height: getRandomNumber(60, 75),
          bmi: 0, // Will be calculated
          painScale: getRandomNumber(0, 10),
          bloodGlucose: Math.random() < 0.3 ? getRandomNumber(80, 200) : undefined,
          recordedBy: getRandomElement(this.users.filter(u => u.role === 'nurse'))._id,
          notes: Math.random() < 0.3 ? 'Patient comfortable during measurement' : undefined,
          isActive: true
        };
        
        // Calculate BMI
        const weightKg = vitalData.weight * 0.453592;
        const heightM = vitalData.height * 0.0254;
        vitalData.bmi = +(weightKg / (heightM * heightM)).toFixed(1);
        
        vitalSignsData.push(vitalData);
      }
    }

    await VitalSigns.insertMany(vitalSignsData);
    console.log(`✅ Created ${vitalSignsData.length} vital signs records`);
  }

  private async seedPatientDocuments() {
    console.log('📄 Seeding patient documents...');
    
    const documentsData = [];
    const documentTypes = [
      'Medical Report', 'Lab Result', 'Imaging Study', 'Prescription', 
      'Consent Form', 'Insurance Document', 'Discharge Summary'
    ];
    
    const categories = ['Clinical', 'Administrative', 'Legal', 'Insurance', 'Personal'];
    
    // Create 2-4 documents per patient
    for (let i = 0; i < this.patients.length; i++) {
      const patient = this.patients[i];
      const numDocs = getRandomNumber(2, 4);
      
      for (let j = 1; j <= numDocs; j++) {
        const docType = getRandomElement(documentTypes);
        const category = getRandomElement(categories);
        
        const docData = {
          documentId: `DOC${String(i * 10 + j).padStart(6, '0')}`,
          patient: patient._id,
          documentName: `${docType} - ${patient.firstName} ${patient.lastName}`,
          documentType: docType,
          category,
          fileName: `${docType.toLowerCase().replace(' ', '_')}_${Date.now()}.pdf`,
          fileSize: getRandomNumber(100000, 5000000), // 100KB to 5MB
          fileType: 'application/pdf',
          fileUrl: `/documents/${patient.patientId}/${docType.toLowerCase().replace(' ', '_')}.pdf`,
          description: `${docType} for patient ${patient.firstName} ${patient.lastName}`,
          keywords: [docType.toLowerCase(), patient.lastName.toLowerCase(), category.toLowerCase()],
          tags: [docType, category],
          clinicalRelevance: getRandomElement(['Low', 'Medium', 'High']),
          isConfidential: Math.random() < 0.3,
          isCritical: Math.random() < 0.2,
          documentDate: getRandomDate(new Date('2023-01-01'), new Date()).toISOString(),
          expiryDate: Math.random() < 0.2 ? getRandomDate(new Date(), new Date('2025-12-31')).toISOString() : undefined,
          sourceType: getRandomElement(['Internal', 'External', 'Patient Provided']),
          uploadedBy: getRandomElement(this.users)._id,
          reviewStatus: getRandomElement(['Pending', 'Approved', 'Rejected', 'Requires Update']),
          accessLevel: getRandomElement(['Public', 'Restricted', 'Confidential']),
          version: 1,
          isLatestVersion: true,
          status: 'Active',
          isActive: true
        };
        
        documentsData.push(docData);
      }
    }

    await PatientDocument.insertMany(documentsData);
    console.log(`✅ Created ${documentsData.length} patient documents`);
  }
}

// Export seeder instance
export const hospitalSeeder = new HospitalSeeder();
