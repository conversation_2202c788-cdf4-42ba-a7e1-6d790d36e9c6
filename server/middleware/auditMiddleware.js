import { getModels } from '../utils/modelLoader.js';

// Audit log middleware to track user actions
export const auditLog = (action, resource = null) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Call original send method
      originalSend.call(this, data);
      
      // Log the action after response is sent
      logAction(req, res, action, resource, data);
    };
    
    next();
  };
};

const logAction = async (req, res, action, resource, responseData) => {
  try {
    const models = getModels();
    if (!models.AuditLog || !req.user) {
      return; // Skip if no audit model or user
    }

    // Parse response to determine status
    let status = 'Success';
    let details = action;
    
    try {
      const parsedData = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
      if (parsedData && !parsedData.success) {
        status = 'Failed';
        details = parsedData.error || parsedData.message || action;
      }
    } catch (e) {
      // If response is not JSON, check status code
      if (res.statusCode >= 400) {
        status = 'Failed';
      }
    }

    // Get client IP address
    const ipAddress = req.ip || 
                     req.connection?.remoteAddress || 
                     req.socket?.remoteAddress ||
                     req.headers['x-forwarded-for']?.split(',')[0] ||
                     '127.0.0.1';

    // Create audit log entry
    const auditData = {
      user: req.user.id || req.user._id,
      action,
      resource,
      resourceId: req.params.id || null,
      details,
      ipAddress,
      userAgent: req.headers['user-agent'] || '',
      status,
      metadata: {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        timestamp: new Date()
      }
    };

    await models.AuditLog.createLog(auditData);
  } catch (error) {
    console.error('Audit log error:', error);
    // Don't throw error to avoid breaking the main request
  }
};

// Specific audit functions for common actions
export const auditLogin = auditLog('User Login', 'auth');
export const auditLogout = auditLog('User Logout', 'auth');
export const auditPatientCreate = auditLog('Patient Created', 'patients');
export const auditPatientUpdate = auditLog('Patient Updated', 'patients');
export const auditPatientDelete = auditLog('Patient Deleted', 'patients');
export const auditRoleCreate = auditLog('Role Created', 'roles');
export const auditRoleUpdate = auditLog('Role Updated', 'roles');
export const auditRoleDelete = auditLog('Role Deleted', 'roles');
export const auditUserCreate = auditLog('User Created', 'users');
export const auditUserUpdate = auditLog('User Updated', 'users');
export const auditUserDelete = auditLog('User Deleted', 'users');

// Function to manually create audit log (for use in controllers)
export const createAuditLog = async (req, action, resource = null, details = null, status = 'Success') => {
  try {
    const models = getModels();
    if (!models.AuditLog || !req.user) {
      return;
    }

    const ipAddress = req.ip || 
                     req.connection?.remoteAddress || 
                     req.socket?.remoteAddress ||
                     req.headers['x-forwarded-for']?.split(',')[0] ||
                     '127.0.0.1';

    const auditData = {
      user: req.user.id || req.user._id,
      action,
      resource,
      resourceId: req.params.id || null,
      details: details || action,
      ipAddress,
      userAgent: req.headers['user-agent'] || '',
      status,
      metadata: {
        method: req.method,
        url: req.originalUrl,
        timestamp: new Date()
      }
    };

    await models.AuditLog.createLog(auditData);
  } catch (error) {
    console.error('Manual audit log error:', error);
  }
};

export default auditLog;
