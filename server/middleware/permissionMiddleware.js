import { getModels } from '../utils/modelLoader.js';

// Permission checking middleware
export const checkPermission = (module, action, resource = '*') => {
  return async (req, res, next) => {
    try {
      // Skip permission check if no user (should be caught by auth middleware first)
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const models = getModels();
      
      // If no permission model available, allow access (fallback)
      if (!models.Permission || !models.Role) {
        return next();
      }

      // Super admin bypass
      if (req.user.role?.name === 'Super Admin' || req.user.role?.level >= 10) {
        return next();
      }

      // Get user's permissions
      let userPermissions = [];
      
      if (req.user.permissions && req.user.permissions.length > 0) {
        // User has specific permissions assigned
        userPermissions = req.user.permissions;
      } else if (req.user.role && req.user.role.defaultPermissions) {
        // Use role's default permissions
        userPermissions = req.user.role.defaultPermissions;
      }

      // If no permissions found, populate from database
      if (userPermissions.length === 0 && req.user.role) {
        const role = await models.Role.findById(req.user.role._id || req.user.role)
          .populate('defaultPermissions');
        
        if (role && role.defaultPermissions) {
          userPermissions = role.defaultPermissions;
        }
      }

      // Check if user has the required permission
      const hasPermission = userPermissions.some(permission => {
        // Handle both populated and non-populated permissions
        const perm = permission._id ? permission : permission;
        
        if (typeof perm === 'string') {
          // If permission is just an ID, we can't check details
          return true; // Allow access for now
        }

        // Check if permission matches
        const moduleMatch = perm.module === module || perm.module === '*';
        const actionMatch = perm.action === action || perm.action === '*';
        const resourceMatch = perm.resource === resource || perm.resource === '*';

        return moduleMatch && actionMatch && resourceMatch;
      });

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: `Access denied. Required permission: ${module}:${action}:${resource}`
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      // In case of error, allow access to prevent system lockout
      next();
    }
  };
};

// Helper function to check if user has specific permission
export const hasPermission = (user, module, action, resource = '*') => {
  try {
    // Super admin bypass
    if (user.role?.name === 'Super Admin' || user.role?.level >= 10) {
      return true;
    }

    // Get user's permissions
    let userPermissions = [];
    
    if (user.permissions && user.permissions.length > 0) {
      userPermissions = user.permissions;
    } else if (user.role && user.role.defaultPermissions) {
      userPermissions = user.role.defaultPermissions;
    }

    // Check if user has the required permission
    return userPermissions.some(permission => {
      const perm = permission._id ? permission : permission;
      
      if (typeof perm === 'string') {
        return true; // Allow access for string permissions
      }

      const moduleMatch = perm.module === module || perm.module === '*';
      const actionMatch = perm.action === action || perm.action === '*';
      const resourceMatch = perm.resource === resource || perm.resource === '*';

      return moduleMatch && actionMatch && resourceMatch;
    });
  } catch (error) {
    console.error('Permission check error:', error);
    return false;
  }
};

// Middleware to check multiple permissions (user needs at least one)
export const checkAnyPermission = (...permissions) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // Super admin bypass
      if (req.user.role?.name === 'Super Admin' || req.user.role?.level >= 10) {
        return next();
      }

      // Check if user has any of the required permissions
      const hasAnyPermission = permissions.some(([module, action, resource]) => {
        return hasPermission(req.user, module, action, resource);
      });

      if (!hasAnyPermission) {
        return res.status(403).json({
          success: false,
          error: 'Access denied. Insufficient permissions.'
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      next();
    }
  };
};

// Middleware to check if user has admin role
export const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  if (req.user.role?.level < 8) {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }

  next();
};

// Middleware to check if user has doctor role
export const requireDoctor = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  if (req.user.role?.name !== 'Doctor' && req.user.role?.level < 7) {
    return res.status(403).json({
      success: false,
      error: 'Doctor access required'
    });
  }

  next();
};

export default checkPermission;
