import express from 'express';
import {
  getPatientVisits,
  getPatientVisit,
  createPatientVisit,
  updatePatientVisit,
  deletePatientVisit,
  getPatientVisitStats
} from '../controllers/patientVisitController.js';
import { protect } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Routes with permission checks
router.route('/')
  .get(checkPermission('clinical', 'view'), getPatientVisits)
  .post(checkPermission('clinical', 'create'), createPatientVisit);

// Stats route
router.get('/stats', checkPermission('clinical', 'view'), getPatientVisitStats);

router.route('/:id')
  .get(checkPermission('clinical', 'view'), getPatientVisit)
  .put(checkPermission('clinical', 'edit'), updatePatientVisit)
  .delete(checkPermission('clinical', 'delete'), deletePatientVisit);

export default router;
