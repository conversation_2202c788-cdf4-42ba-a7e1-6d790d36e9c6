import express from 'express';
import {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  getPatientStats
} from '../controllers/patientController.js';
import { protect } from '../controllers/authController.js';
import { checkPermission } from '../middleware/authMiddleware.js';
import { auditPatientCreate, auditPatientUpdate, auditPatientDelete } from '../middleware/auditMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Routes with permission checks
router.route('/')
  .get(checkPermission('patients', 'view'), getPatients)
  .post(checkPermission('patients', 'create'), auditPatientCreate, createPatient);

// Stats route
router.get('/stats', checkPermission('patients', 'view'), getPatientStats);

router.route('/:id')
  .get(checkPermission('patients', 'view'), getPatient)
  .put(checkPermission('patients', 'edit'), auditPatientUpdate, updatePatient)
  .delete(checkPermission('patients', 'delete'), auditPatientDelete, deletePatient);

export default router;
