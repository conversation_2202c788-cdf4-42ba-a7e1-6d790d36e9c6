const express = require('express');
const patientVisitController = require('../controllers/patientVisitController');
const { protect } = require('../controllers/authController');
const { checkPermission } = require('../middleware/authMiddleware');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

router.route('/')
  .get(checkPermission('patients', 'view', 'visits'), patientVisitController.getPatientVisits)
  .post(checkPermission('patients', 'create', 'visits'), patientVisitController.createPatientVisit);

router.route('/:id')
  .get(checkPermission('patients', 'view', 'visits'), patientVisitController.getPatientVisitById)
  .put(checkPermission('patients', 'edit', 'visits'), patientVisitController.updatePatientVisit)
  .delete(checkPermission('patients', 'delete', 'visits'), patientVisitController.deletePatientVisit);

// Routes
router.get('/stats', patientVisitController.getPatientVisitStats);

module.exports = router;