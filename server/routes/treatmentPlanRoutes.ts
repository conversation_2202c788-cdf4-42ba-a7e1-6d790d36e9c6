import express from 'express';
import { body, query, param } from 'express-validator';
import {
  getTreatmentPlans,
  getTreatmentPlan,
  createTreatmentPlan,
  updateTreatmentPlan,
  deleteTreatmentPlan,
  getPatientTreatmentPlans,
  updateTreatmentPlanStatus,
  addProgressNote,
  getTreatmentPlanStats
} from '../controllers/treatmentPlanController.js';
import { protect, checkPermission } from '../middleware/authMiddleware.js';

const router = express.Router();

// Validation rules
const createTreatmentPlanValidation = [
  body('patient').isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').isMongoId().withMessage('Valid doctor ID is required'),
  body('planName').notEmpty().withMessage('Plan name is required'),
  body('planType').isIn(['Acute', 'Chronic', 'Preventive', 'Rehabilitation', 'Palliative'])
    .withMessage('Valid plan type is required'),
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('priority').isIn(['Low', 'Medium', 'High', 'Critical']).withMessage('Valid priority is required'),
  
  // Primary condition validation
  body('primaryCondition.diagnosis').notEmpty().withMessage('Primary diagnosis is required'),
  body('primaryCondition.severity').isIn(['Mild', 'Moderate', 'Severe'])
    .withMessage('Valid severity is required'),
  
  // Goals validation
  body('goals').optional().isArray().withMessage('Goals must be an array'),
  body('goals.*.description').optional().notEmpty().withMessage('Goal description is required'),
  body('goals.*.status').optional().isIn(['Not Started', 'In Progress', 'Achieved', 'Modified', 'Discontinued'])
    .withMessage('Valid goal status is required'),
  
  // Medications validation
  body('medications').optional().isArray().withMessage('Medications must be an array'),
  body('medications.*.name').optional().notEmpty().withMessage('Medication name is required'),
  body('medications.*.dosage').optional().notEmpty().withMessage('Medication dosage is required'),
  body('medications.*.frequency').optional().notEmpty().withMessage('Medication frequency is required'),
  body('medications.*.route').optional().isIn(['Oral', 'IV', 'IM', 'Topical', 'Inhaled', 'Sublingual', 'Other'])
    .withMessage('Valid medication route is required'),
  body('medications.*.startDate').optional().isISO8601().withMessage('Valid medication start date is required'),
  body('medications.*.instructions').optional().notEmpty().withMessage('Medication instructions are required'),
  body('medications.*.prescribedBy').optional().isMongoId().withMessage('Valid prescriber ID is required'),
  body('medications.*.status').optional().isIn(['Active', 'Completed', 'Discontinued', 'On Hold'])
    .withMessage('Valid medication status is required'),
  
  // Therapies validation
  body('therapies').optional().isArray().withMessage('Therapies must be an array'),
  body('therapies.*.type').optional().isIn(['Physical Therapy', 'Occupational Therapy', 'Speech Therapy', 'Respiratory Therapy', 'Psychological Therapy', 'Other'])
    .withMessage('Valid therapy type is required'),
  body('therapies.*.description').optional().notEmpty().withMessage('Therapy description is required'),
  body('therapies.*.frequency').optional().notEmpty().withMessage('Therapy frequency is required'),
  body('therapies.*.duration').optional().notEmpty().withMessage('Therapy duration is required'),
  body('therapies.*.startDate').optional().isISO8601().withMessage('Valid therapy start date is required'),
  
  // Monitoring validation
  body('monitoring.vitalSigns.frequency').optional().notEmpty().withMessage('Vital signs frequency is required'),
  body('monitoring.labTests').optional().isArray().withMessage('Lab tests must be an array'),
  body('monitoring.labTests.*.testName').optional().notEmpty().withMessage('Lab test name is required'),
  body('monitoring.labTests.*.frequency').optional().notEmpty().withMessage('Lab test frequency is required'),
  
  // Lifestyle modifications validation
  body('lifestyleModifications').optional().isArray().withMessage('Lifestyle modifications must be an array'),
  body('lifestyleModifications.*.category').optional().isIn(['Diet', 'Exercise', 'Sleep', 'Stress Management', 'Smoking Cessation', 'Alcohol Reduction', 'Other'])
    .withMessage('Valid lifestyle modification category is required'),
  body('lifestyleModifications.*.description').optional().notEmpty().withMessage('Lifestyle modification description is required'),
  body('lifestyleModifications.*.instructions').optional().notEmpty().withMessage('Lifestyle modification instructions are required'),
  
  // Care team validation
  body('careTeam').optional().isArray().withMessage('Care team must be an array'),
  body('careTeam.*.member').optional().isMongoId().withMessage('Valid care team member ID is required'),
  body('careTeam.*.role').optional().notEmpty().withMessage('Care team member role is required'),
  body('careTeam.*.responsibilities').optional().isArray().withMessage('Responsibilities must be an array'),
  
  body('planNotes').notEmpty().withMessage('Plan notes are required'),
  body('status').optional().isIn(['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'])
    .withMessage('Invalid status')
];

const updateTreatmentPlanValidation = [
  body('patient').optional().isMongoId().withMessage('Valid patient ID is required'),
  body('doctor').optional().isMongoId().withMessage('Valid doctor ID is required'),
  body('planName').optional().notEmpty().withMessage('Plan name cannot be empty'),
  body('planType').optional().isIn(['Acute', 'Chronic', 'Preventive', 'Rehabilitation', 'Palliative'])
    .withMessage('Valid plan type is required'),
  body('startDate').optional().isISO8601().withMessage('Valid start date is required'),
  body('endDate').optional().isISO8601().withMessage('Valid end date is required'),
  body('priority').optional().isIn(['Low', 'Medium', 'High', 'Critical']).withMessage('Valid priority is required'),
  
  // Similar validations as create but all optional
  body('primaryCondition.diagnosis').optional().notEmpty().withMessage('Primary diagnosis cannot be empty'),
  body('primaryCondition.severity').optional().isIn(['Mild', 'Moderate', 'Severe'])
    .withMessage('Valid severity is required'),
  
  body('planNotes').optional().notEmpty().withMessage('Plan notes cannot be empty'),
  body('status').optional().isIn(['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'])
    .withMessage('Invalid status')
];

const queryValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('patient').optional().isMongoId().withMessage('Valid patient ID is required'),
  query('doctor').optional().isMongoId().withMessage('Valid doctor ID is required'),
  query('status').optional().isIn(['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'])
    .withMessage('Invalid status'),
  query('planType').optional().isIn(['Acute', 'Chronic', 'Preventive', 'Rehabilitation', 'Palliative'])
    .withMessage('Invalid plan type'),
  query('priority').optional().isIn(['Low', 'Medium', 'High', 'Critical']).withMessage('Invalid priority'),
  query('startDate').optional().isISO8601().withMessage('Valid start date is required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date is required'),
  query('search').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters')
];

const paramValidation = [
  param('id').isMongoId().withMessage('Valid treatment plan ID is required')
];

const patientParamValidation = [
  param('patientId').isMongoId().withMessage('Valid patient ID is required')
];

const statusUpdateValidation = [
  body('status').isIn(['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'])
    .withMessage('Valid status is required')
];

const progressNoteValidation = [
  body('type').isIn(['Assessment', 'Progress Update', 'Medication Change', 'Goal Modification', 'Other'])
    .withMessage('Valid progress note type is required'),
  body('content').notEmpty().withMessage('Progress note content is required'),
  body('attachments').optional().isArray().withMessage('Attachments must be an array')
];

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(checkPermission('patients', 'view'), queryValidation, getTreatmentPlans)
  .post(checkPermission('patients', 'create'), createTreatmentPlanValidation, createTreatmentPlan);

// Stats route
router.get('/stats', checkPermission('patients', 'view'), getTreatmentPlanStats);

// Patient treatment plans
router.get('/patient/:patientId', 
  checkPermission('patients', 'view'), 
  patientParamValidation, 
  getPatientTreatmentPlans
);

// Individual treatment plan routes
router.route('/:id')
  .get(checkPermission('patients', 'view'), paramValidation, getTreatmentPlan)
  .put(checkPermission('patients', 'edit'), paramValidation, updateTreatmentPlanValidation, updateTreatmentPlan)
  .delete(checkPermission('patients', 'delete'), paramValidation, deleteTreatmentPlan);

// Status update route
router.patch('/:id/status', 
  checkPermission('patients', 'edit'), 
  paramValidation, 
  statusUpdateValidation, 
  updateTreatmentPlanStatus
);

// Progress notes route
router.post('/:id/progress-notes', 
  checkPermission('patients', 'edit'), 
  paramValidation, 
  progressNoteValidation, 
  addProgressNote
);

export default router;
