import mongoose, { Schema, Document } from 'mongoose';

export interface IPatient extends Document {
  patientId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  dateOfBirth: Date;
  gender: 'Male' | 'Female' | 'Other';
  email?: string;
  phone: string;
  alternatePhone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
    address?: string;
  };
  insurance?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
    expiryDate?: Date;
    copayAmount?: number;
    deductibleAmount?: number;
    coverageType?: string;
  };
  medicalHistory: {
    allergies: Array<{
      allergen: string;
      reaction: string;
      severity: 'Mild' | 'Moderate' | 'Severe';
      dateIdentified?: Date;
    }>;
    chronicConditions: Array<{
      condition: string;
      diagnosedDate?: Date;
      status: 'Active' | 'Controlled' | 'Resolved';
      notes?: string;
    }>;
    medications: Array<{
      name: string;
      dosage: string;
      frequency: string;
      startDate?: Date;
      endDate?: Date;
      prescribedBy?: string;
      status: 'Active' | 'Discontinued' | 'Completed';
    }>;
    surgeries: Array<{
      procedure: string;
      date: Date;
      hospital?: string;
      surgeon?: string;
      complications?: string;
      outcome?: string;
    }>;
    familyHistory: Array<{
      relationship: string;
      condition: string;
      ageAtDiagnosis?: number;
      notes?: string;
    }>;
    socialHistory: {
      smokingStatus: 'Never' | 'Former' | 'Current';
      alcoholUse: 'None' | 'Occasional' | 'Regular' | 'Heavy';
      drugUse?: string;
      occupation?: string;
      maritalStatus?: 'Single' | 'Married' | 'Divorced' | 'Widowed';
      lifestyle?: string;
    };
  };
  bloodType?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
  status: 'Active' | 'Inactive' | 'Deceased';
  admissionDate?: Date;
  dischargeDate?: Date;
  assignedDoctor?: mongoose.Types.ObjectId;
  room?: string;
  notes?: string;
  isActive: boolean;
}

const PatientSchema: Schema = new Schema({
  patientId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'PT' + Date.now().toString().slice(-6);
    }
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  dateOfBirth: {
    type: Date,
    required: true
  },
  gender: {
    type: String,
    required: true,
    enum: ['Male', 'Female', 'Other']
  },
  email: {
    type: String,
    lowercase: true,
    trim: true,
    sparse: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true, default: 'USA' }
  },
  emergencyContact: {
    name: { type: String, required: true },
    relationship: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String }
  },
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String,
    expiryDate: Date
  },
  medicalHistory: {
    allergies: [String],
    chronicConditions: [String],
    medications: [String],
    surgeries: [{
      procedure: String,
      date: Date,
      hospital: String
    }]
  },
  bloodType: {
    type: String,
    enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Deceased'],
    default: 'Active'
  },
  admissionDate: Date,
  dischargeDate: Date,
  assignedDoctor: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  room: String,
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better performance
PatientSchema.index({ patientId: 1 });
PatientSchema.index({ email: 1 });
PatientSchema.index({ phone: 1 });
PatientSchema.index({ firstName: 1, lastName: 1 });
PatientSchema.index({ assignedDoctor: 1 });

// Virtual for full name
PatientSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for age
PatientSchema.virtual('age').get(function() {
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

export default mongoose.model<IPatient>('Patient', PatientSchema);
