import mongoose from 'mongoose';

const patientVisitSchema = new mongoose.Schema({
  visitId: {
    type: String,
    required: true,
    unique: true
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visitDate: {
    type: Date,
    required: true
  },
  visitTime: {
    type: String
  },
  visitType: {
    type: String,
    enum: ['Consultation', 'Follow-up', 'Emergency', 'Routine Check-up', 'Procedure', 'Vaccination', 'Screening'],
    required: true
  },
  department: {
    type: String,
    required: true
  },
  chiefComplaint: {
    type: String,
    required: true
  },
  historyOfPresentIllness: {
    type: String
  },
  reviewOfSystems: {
    constitutional: String,
    cardiovascular: String,
    respiratory: String,
    gastrointestinal: String,
    genitourinary: String,
    musculoskeletal: String,
    neurological: String,
    psychiatric: String,
    endocrine: String,
    hematologic: String,
    allergic: String
  },
  physicalExamination: {
    vitalSigns: {
      temperature: Number,
      bloodPressure: {
        systolic: Number,
        diastolic: Number
      },
      heartRate: Number,
      respiratoryRate: Number,
      oxygenSaturation: Number,
      weight: Number,
      height: Number,
      bmi: Number,
      painScale: Number
    },
    generalAppearance: String,
    heent: String, // Head, Eyes, Ears, Nose, Throat
    cardiovascular: String,
    respiratory: String,
    abdominal: String,
    musculoskeletal: String,
    neurological: String,
    skin: String,
    psychiatric: String
  },
  assessment: {
    primaryDiagnosis: {
      code: String,
      description: String,
      severity: {
        type: String,
        enum: ['Mild', 'Moderate', 'Severe', 'Critical']
      }
    },
    secondaryDiagnoses: [{
      code: String,
      description: String,
      severity: {
        type: String,
        enum: ['Mild', 'Moderate', 'Severe', 'Critical']
      }
    }],
    differentialDiagnoses: [String],
    clinicalImpression: String
  },
  plan: {
    medications: [{
      name: String,
      dosage: String,
      frequency: String,
      duration: String,
      route: String,
      instructions: String,
      prescribedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      startDate: Date,
      endDate: Date,
      status: {
        type: String,
        enum: ['Active', 'Discontinued', 'Completed'],
        default: 'Active'
      }
    }],
    procedures: [{
      name: String,
      description: String,
      scheduledDate: Date,
      performedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      status: {
        type: String,
        enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled'],
        default: 'Scheduled'
      },
      notes: String
    }],
    labOrders: [{
      testName: String,
      testCode: String,
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      orderedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      orderDate: Date,
      status: {
        type: String,
        enum: ['Ordered', 'Collected', 'Processing', 'Completed', 'Cancelled'],
        default: 'Ordered'
      },
      results: String,
      resultDate: Date
    }],
    imagingOrders: [{
      studyType: String,
      bodyPart: String,
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      orderedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      orderDate: Date,
      status: {
        type: String,
        enum: ['Ordered', 'Scheduled', 'In Progress', 'Completed', 'Cancelled'],
        default: 'Ordered'
      },
      findings: String,
      resultDate: Date
    }],
    referrals: [{
      specialty: String,
      provider: String,
      reason: String,
      urgency: {
        type: String,
        enum: ['Routine', 'Urgent', 'STAT'],
        default: 'Routine'
      },
      referredBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      referralDate: Date,
      status: {
        type: String,
        enum: ['Pending', 'Scheduled', 'Completed', 'Cancelled'],
        default: 'Pending'
      }
    }],
    followUpInstructions: String,
    followUpDate: Date,
    patientEducation: [String],
    dietaryRestrictions: [String],
    activityRestrictions: [String]
  },
  status: {
    type: String,
    enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'No Show'],
    default: 'Scheduled'
  },
  duration: {
    type: Number, // in minutes
    default: 30
  },
  notes: {
    type: String
  },
  billing: {
    charges: [{
      description: String,
      code: String,
      amount: Number,
      quantity: {
        type: Number,
        default: 1
      }
    }],
    totalAmount: Number,
    insuranceCoverage: Number,
    patientResponsibility: Number,
    paymentStatus: {
      type: String,
      enum: ['Pending', 'Partial', 'Paid', 'Denied'],
      default: 'Pending'
    }
  },
  qualityMetrics: {
    patientSatisfactionScore: Number,
    waitTime: Number, // in minutes
    visitDuration: Number, // in minutes
    followUpCompliance: Boolean
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
patientVisitSchema.index({ patient: 1, visitDate: -1 });
patientVisitSchema.index({ doctor: 1, visitDate: -1 });
patientVisitSchema.index({ visitDate: -1 });
patientVisitSchema.index({ status: 1 });
patientVisitSchema.index({ department: 1 });
patientVisitSchema.index({ visitType: 1 });

// Virtual for visit duration in hours
patientVisitSchema.virtual('durationHours').get(function() {
  return this.duration ? (this.duration / 60).toFixed(2) : 0;
});

// Virtual for patient age at visit
patientVisitSchema.virtual('patientAgeAtVisit').get(function() {
  if (this.patient && this.patient.dateOfBirth && this.visitDate) {
    const ageInMs = this.visitDate - new Date(this.patient.dateOfBirth);
    return Math.floor(ageInMs / (365.25 * 24 * 60 * 60 * 1000));
  }
  return null;
});

// Pre-save middleware to calculate BMI if height and weight are provided
patientVisitSchema.pre('save', function(next) {
  if (this.physicalExamination && 
      this.physicalExamination.vitalSigns && 
      this.physicalExamination.vitalSigns.height && 
      this.physicalExamination.vitalSigns.weight) {
    const heightInMeters = this.physicalExamination.vitalSigns.height / 100;
    this.physicalExamination.vitalSigns.bmi = 
      (this.physicalExamination.vitalSigns.weight / (heightInMeters * heightInMeters)).toFixed(1);
  }
  next();
});

// Static method to get visit statistics
patientVisitSchema.statics.getVisitStats = async function(startDate, endDate) {
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.visitDate = {};
    if (startDate) matchStage.visitDate.$gte = new Date(startDate);
    if (endDate) matchStage.visitDate.$lte = new Date(endDate);
  }

  return await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalVisits: { $sum: 1 },
        completedVisits: {
          $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] }
        },
        cancelledVisits: {
          $sum: { $cond: [{ $eq: ['$status', 'Cancelled'] }, 1, 0] }
        },
        noShowVisits: {
          $sum: { $cond: [{ $eq: ['$status', 'No Show'] }, 1, 0] }
        },
        avgDuration: { $avg: '$duration' },
        avgWaitTime: { $avg: '$qualityMetrics.waitTime' }
      }
    }
  ]);
};

const PatientVisit = mongoose.model('PatientVisit', patientVisitSchema);

export default PatientVisit;
