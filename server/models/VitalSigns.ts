import mongoose, { Schema, Document } from 'mongoose';

export interface IVitalSigns extends Document {
  vitalSignsId: string;
  patient: mongoose.Types.ObjectId;
  visit?: mongoose.Types.ObjectId;
  recordedBy: mongoose.Types.ObjectId;
  recordedAt: Date;
  
  // Basic vital signs
  temperature: {
    value: number;
    unit: 'Celsius' | 'Fahrenheit';
    method: 'Oral' | 'Rectal' | 'Axillary' | 'Tympanic' | 'Temporal';
  };
  
  bloodPressure: {
    systolic: number;
    diastolic: number;
    unit: 'mmHg';
    position: 'Sitting' | 'Standing' | 'Lying';
    arm: 'Left' | 'Right';
    cuffSize?: 'Small' | 'Regular' | 'Large' | 'Extra Large';
  };
  
  heartRate: {
    value: number;
    unit: 'bpm';
    rhythm: 'Regular' | 'Irregular';
    method: 'Palpation' | 'Auscultation' | 'Monitor';
  };
  
  respiratoryRate: {
    value: number;
    unit: 'breaths/min';
    effort: 'Normal' | 'Labored' | 'Shallow' | 'Deep';
    pattern: 'Regular' | 'Irregular';
  };
  
  oxygenSaturation: {
    value: number;
    unit: '%';
    onRoomAir: boolean;
    oxygenFlow?: number;
    oxygenDevice?: 'Nasal Cannula' | 'Face Mask' | 'Non-Rebreather' | 'Ventilator' | 'CPAP' | 'BiPAP';
  };
  
  // Physical measurements
  weight: {
    value: number;
    unit: 'kg' | 'lbs';
    clothed: boolean;
    scale: 'Digital' | 'Mechanical' | 'Bed Scale';
  };
  
  height: {
    value: number;
    unit: 'cm' | 'inches';
    method: 'Standing' | 'Lying' | 'Estimated';
  };
  
  bmi?: {
    value: number;
    category: 'Underweight' | 'Normal' | 'Overweight' | 'Obese Class I' | 'Obese Class II' | 'Obese Class III';
  };
  
  // Pain assessment
  painAssessment?: {
    scale: '0-10 Numeric' | 'Wong-Baker FACES' | 'FLACC' | 'PQRST';
    score: number;
    location?: string;
    quality?: string;
    duration?: string;
    aggravatingFactors?: string;
    relievingFactors?: string;
  };
  
  // Neurological assessments
  glasgowComaScale?: {
    eyeOpening: number;
    verbalResponse: number;
    motorResponse: number;
    total: number;
  };
  
  pupilAssessment?: {
    left: {
      size: number;
      reactivity: 'Reactive' | 'Sluggish' | 'Non-reactive';
      shape: 'Round' | 'Irregular';
    };
    right: {
      size: number;
      reactivity: 'Reactive' | 'Sluggish' | 'Non-reactive';
      shape: 'Round' | 'Irregular';
    };
  };
  
  // Additional measurements
  headCircumference?: {
    value: number;
    unit: 'cm' | 'inches';
  };
  
  waistCircumference?: {
    value: number;
    unit: 'cm' | 'inches';
  };
  
  // Blood glucose
  bloodGlucose?: {
    value: number;
    unit: 'mg/dL' | 'mmol/L';
    timing: 'Fasting' | 'Random' | 'Post-meal' | 'Bedtime';
    method: 'Fingerstick' | 'Venous' | 'Continuous Monitor';
  };
  
  // Peak flow (for respiratory conditions)
  peakFlow?: {
    value: number;
    unit: 'L/min';
    predicted?: number;
    percentPredicted?: number;
  };
  
  // Orthostatic vital signs
  orthostaticVitals?: {
    lying: {
      systolic: number;
      diastolic: number;
      heartRate: number;
    };
    sitting: {
      systolic: number;
      diastolic: number;
      heartRate: number;
    };
    standing: {
      systolic: number;
      diastolic: number;
      heartRate: number;
    };
    symptoms?: string[];
  };
  
  // Context and notes
  context: 'Routine' | 'Pre-procedure' | 'Post-procedure' | 'Emergency' | 'Monitoring' | 'Discharge';
  location: 'Emergency Room' | 'ICU' | 'Medical Ward' | 'Outpatient Clinic' | 'Operating Room' | 'Recovery Room' | 'Home' | 'Other';
  
  // Quality indicators
  qualityFlags: Array<{
    flag: 'Abnormal' | 'Critical' | 'Trending Up' | 'Trending Down' | 'Stable' | 'Equipment Issue';
    description?: string;
  }>;
  
  // Alerts and notifications
  alerts: Array<{
    type: 'Critical Value' | 'Trending Alert' | 'Parameter Out of Range';
    parameter: string;
    message: string;
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    acknowledged: boolean;
    acknowledgedBy?: mongoose.Types.ObjectId;
    acknowledgedAt?: Date;
  }>;
  
  // Equipment used
  equipment?: Array<{
    type: string;
    model?: string;
    serialNumber?: string;
    calibrationDate?: Date;
  }>;
  
  // Validation and verification
  verified: boolean;
  verifiedBy?: mongoose.Types.ObjectId;
  verifiedAt?: Date;
  
  // Notes and observations
  notes?: string;
  clinicalObservations?: string;
  
  // Data source
  dataSource: 'Manual Entry' | 'Monitor Import' | 'Device Integration' | 'Patient Reported';
  
  // Metadata
  isActive: boolean;
  correctedBy?: mongoose.Types.ObjectId;
  correctionReason?: string;
  correctedAt?: Date;
}

const VitalSignsSchema: Schema = new Schema({
  vitalSignsId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'VS' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  visit: {
    type: Schema.Types.ObjectId,
    ref: 'PatientVisit'
  },
  recordedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recordedAt: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  temperature: {
    value: { type: Number, required: true, min: 90, max: 110 },
    unit: {
      type: String,
      enum: ['Celsius', 'Fahrenheit'],
      default: 'Celsius'
    },
    method: {
      type: String,
      enum: ['Oral', 'Rectal', 'Axillary', 'Tympanic', 'Temporal'],
      default: 'Oral'
    }
  },
  bloodPressure: {
    systolic: { type: Number, required: true, min: 60, max: 250 },
    diastolic: { type: Number, required: true, min: 40, max: 150 },
    unit: { type: String, default: 'mmHg' },
    position: {
      type: String,
      enum: ['Sitting', 'Standing', 'Lying'],
      default: 'Sitting'
    },
    arm: {
      type: String,
      enum: ['Left', 'Right'],
      default: 'Left'
    },
    cuffSize: {
      type: String,
      enum: ['Small', 'Regular', 'Large', 'Extra Large']
    }
  },
  heartRate: {
    value: { type: Number, required: true, min: 30, max: 200 },
    unit: { type: String, default: 'bpm' },
    rhythm: {
      type: String,
      enum: ['Regular', 'Irregular'],
      default: 'Regular'
    },
    method: {
      type: String,
      enum: ['Palpation', 'Auscultation', 'Monitor'],
      default: 'Palpation'
    }
  },
  respiratoryRate: {
    value: { type: Number, required: true, min: 8, max: 40 },
    unit: { type: String, default: 'breaths/min' },
    effort: {
      type: String,
      enum: ['Normal', 'Labored', 'Shallow', 'Deep'],
      default: 'Normal'
    },
    pattern: {
      type: String,
      enum: ['Regular', 'Irregular'],
      default: 'Regular'
    }
  },
  oxygenSaturation: {
    value: { type: Number, required: true, min: 70, max: 100 },
    unit: { type: String, default: '%' },
    onRoomAir: { type: Boolean, default: true },
    oxygenFlow: { type: Number, min: 0, max: 15 },
    oxygenDevice: {
      type: String,
      enum: ['Nasal Cannula', 'Face Mask', 'Non-Rebreather', 'Ventilator', 'CPAP', 'BiPAP']
    }
  },
  weight: {
    value: { type: Number, required: true, min: 0, max: 1000 },
    unit: {
      type: String,
      enum: ['kg', 'lbs'],
      default: 'kg'
    },
    clothed: { type: Boolean, default: true },
    scale: {
      type: String,
      enum: ['Digital', 'Mechanical', 'Bed Scale'],
      default: 'Digital'
    }
  },
  height: {
    value: { type: Number, required: true, min: 0, max: 300 },
    unit: {
      type: String,
      enum: ['cm', 'inches'],
      default: 'cm'
    },
    method: {
      type: String,
      enum: ['Standing', 'Lying', 'Estimated'],
      default: 'Standing'
    }
  },
  bmi: {
    value: Number,
    category: {
      type: String,
      enum: ['Underweight', 'Normal', 'Overweight', 'Obese Class I', 'Obese Class II', 'Obese Class III']
    }
  },
  painAssessment: {
    scale: {
      type: String,
      enum: ['0-10 Numeric', 'Wong-Baker FACES', 'FLACC', 'PQRST']
    },
    score: { type: Number, min: 0, max: 10 },
    location: String,
    quality: String,
    duration: String,
    aggravatingFactors: String,
    relievingFactors: String
  },
  glasgowComaScale: {
    eyeOpening: { type: Number, min: 1, max: 4 },
    verbalResponse: { type: Number, min: 1, max: 5 },
    motorResponse: { type: Number, min: 1, max: 6 },
    total: { type: Number, min: 3, max: 15 }
  },
  pupilAssessment: {
    left: {
      size: { type: Number, min: 1, max: 8 },
      reactivity: {
        type: String,
        enum: ['Reactive', 'Sluggish', 'Non-reactive']
      },
      shape: {
        type: String,
        enum: ['Round', 'Irregular']
      }
    },
    right: {
      size: { type: Number, min: 1, max: 8 },
      reactivity: {
        type: String,
        enum: ['Reactive', 'Sluggish', 'Non-reactive']
      },
      shape: {
        type: String,
        enum: ['Round', 'Irregular']
      }
    }
  },
  headCircumference: {
    value: { type: Number, min: 0, max: 100 },
    unit: {
      type: String,
      enum: ['cm', 'inches'],
      default: 'cm'
    }
  },
  waistCircumference: {
    value: { type: Number, min: 0, max: 200 },
    unit: {
      type: String,
      enum: ['cm', 'inches'],
      default: 'cm'
    }
  },
  bloodGlucose: {
    value: { type: Number, min: 0, max: 1000 },
    unit: {
      type: String,
      enum: ['mg/dL', 'mmol/L'],
      default: 'mg/dL'
    },
    timing: {
      type: String,
      enum: ['Fasting', 'Random', 'Post-meal', 'Bedtime']
    },
    method: {
      type: String,
      enum: ['Fingerstick', 'Venous', 'Continuous Monitor']
    }
  },
  peakFlow: {
    value: { type: Number, min: 0, max: 1000 },
    unit: { type: String, default: 'L/min' },
    predicted: Number,
    percentPredicted: Number
  },
  orthostaticVitals: {
    lying: {
      systolic: Number,
      diastolic: Number,
      heartRate: Number
    },
    sitting: {
      systolic: Number,
      diastolic: Number,
      heartRate: Number
    },
    standing: {
      systolic: Number,
      diastolic: Number,
      heartRate: Number
    },
    symptoms: [String]
  },
  context: {
    type: String,
    required: true,
    enum: ['Routine', 'Pre-procedure', 'Post-procedure', 'Emergency', 'Monitoring', 'Discharge'],
    default: 'Routine'
  },
  location: {
    type: String,
    required: true,
    enum: ['Emergency Room', 'ICU', 'Medical Ward', 'Outpatient Clinic', 'Operating Room', 'Recovery Room', 'Home', 'Other'],
    default: 'Outpatient Clinic'
  },
  qualityFlags: [{
    flag: {
      type: String,
      enum: ['Abnormal', 'Critical', 'Trending Up', 'Trending Down', 'Stable', 'Equipment Issue'],
      required: true
    },
    description: String
  }],
  alerts: [{
    type: {
      type: String,
      enum: ['Critical Value', 'Trending Alert', 'Parameter Out of Range'],
      required: true
    },
    parameter: { type: String, required: true },
    message: { type: String, required: true },
    severity: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical'],
      required: true
    },
    acknowledged: { type: Boolean, default: false },
    acknowledgedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    acknowledgedAt: Date
  }],
  equipment: [{
    type: { type: String, required: true },
    model: String,
    serialNumber: String,
    calibrationDate: Date
  }],
  verified: {
    type: Boolean,
    default: false
  },
  verifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: Date,
  notes: {
    type: String,
    trim: true
  },
  clinicalObservations: {
    type: String,
    trim: true
  },
  dataSource: {
    type: String,
    required: true,
    enum: ['Manual Entry', 'Monitor Import', 'Device Integration', 'Patient Reported'],
    default: 'Manual Entry'
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  correctedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  correctionReason: {
    type: String,
    trim: true
  },
  correctedAt: Date
}, {
  timestamps: true
});

// Indexes for better performance
VitalSignsSchema.index({ vitalSignsId: 1 });
VitalSignsSchema.index({ patient: 1, recordedAt: -1 });
VitalSignsSchema.index({ patient: 1, context: 1, recordedAt: -1 });
VitalSignsSchema.index({ recordedBy: 1, recordedAt: -1 });
VitalSignsSchema.index({ visit: 1 });
VitalSignsSchema.index({ location: 1, recordedAt: -1 });

// Compound indexes
VitalSignsSchema.index({ patient: 1, isActive: 1, recordedAt: -1 });

// Pre-save middleware to calculate BMI
VitalSignsSchema.pre('save', function(next) {
  if (this.weight?.value && this.height?.value) {
    let weightInKg = this.weight.value;
    let heightInM = this.height.value;
    
    // Convert weight to kg if in lbs
    if (this.weight.unit === 'lbs') {
      weightInKg = this.weight.value * 0.453592;
    }
    
    // Convert height to meters if in inches
    if (this.height.unit === 'inches') {
      heightInM = this.height.value * 0.0254;
    } else {
      heightInM = this.height.value / 100; // cm to m
    }
    
    const bmiValue = weightInKg / (heightInM * heightInM);
    
    let category = 'Normal';
    if (bmiValue < 18.5) category = 'Underweight';
    else if (bmiValue >= 25 && bmiValue < 30) category = 'Overweight';
    else if (bmiValue >= 30 && bmiValue < 35) category = 'Obese Class I';
    else if (bmiValue >= 35 && bmiValue < 40) category = 'Obese Class II';
    else if (bmiValue >= 40) category = 'Obese Class III';
    
    this.bmi = {
      value: Number(bmiValue.toFixed(1)),
      category: category
    };
  }
  
  // Calculate Glasgow Coma Scale total
  if (this.glasgowComaScale?.eyeOpening && this.glasgowComaScale?.verbalResponse && this.glasgowComaScale?.motorResponse) {
    this.glasgowComaScale.total = this.glasgowComaScale.eyeOpening + this.glasgowComaScale.verbalResponse + this.glasgowComaScale.motorResponse;
  }
  
  next();
});

export default mongoose.model<IVitalSigns>('VitalSigns', VitalSignsSchema);
