import mongoose, { Schema, Document } from 'mongoose';

export interface IPatientCommunication extends Document {
  communicationId: string;
  patient: mongoose.Types.ObjectId;
  sender: mongoose.Types.ObjectId;
  recipient?: mongoose.Types.ObjectId;
  
  // Communication details
  type: 'Appointment Reminder' | 'Treatment Notification' | 'Follow-up Alert' | 'Lab Results' | 
        'Prescription Ready' | 'Insurance Update' | 'General Message' | 'Emergency Alert' | 
        'Educational Material' | 'Survey Request' | 'Payment Reminder' | 'Discharge Instructions';
  
  method: 'Email' | 'SMS' | 'Phone Call' | 'In-App Notification' | 'Portal Message' | 'Letter';
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  
  // Message content
  subject: string;
  message: string;
  templateUsed?: string;
  
  // Scheduling
  scheduledAt?: Date;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  
  // Status tracking
  status: 'Draft' | 'Scheduled' | 'Sent' | 'Delivered' | 'Read' | 'Failed' | 'Cancelled';
  deliveryStatus: 'Pending' | 'Delivered' | 'Failed' | 'Bounced' | 'Undeliverable';
  
  // Response tracking
  responseRequired: boolean;
  responseReceived: boolean;
  responseDate?: Date;
  responseContent?: string;
  
  // Related entities
  appointment?: mongoose.Types.ObjectId;
  visit?: mongoose.Types.ObjectId;
  treatmentPlan?: mongoose.Types.ObjectId;
  bill?: mongoose.Types.ObjectId;
  
  // Delivery details
  deliveryDetails: {
    emailAddress?: string;
    phoneNumber?: string;
    deliveryAttempts: number;
    lastAttemptAt?: Date;
    errorMessage?: string;
    providerResponse?: string;
  };
  
  // Personalization
  personalizedContent: Array<{
    placeholder: string;
    value: string;
  }>;
  
  // Attachments
  attachments: Array<{
    fileName: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
  }>;
  
  // Preferences and consent
  consentGiven: boolean;
  consentDate?: Date;
  optOutRequested: boolean;
  optOutDate?: Date;
  
  // Campaign tracking
  campaignId?: string;
  campaignName?: string;
  
  // Analytics
  analytics: {
    opened: boolean;
    openedAt?: Date;
    clicked: boolean;
    clickedAt?: Date;
    clickedLinks: Array<{
      url: string;
      clickedAt: Date;
    }>;
  };
  
  // Follow-up
  followUpRequired: boolean;
  followUpDate?: Date;
  followUpCompleted: boolean;
  followUpNotes?: string;
  
  // Metadata
  tags: string[];
  notes?: string;
  isActive: boolean;
  
  // Audit
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
}

const PatientCommunicationSchema: Schema = new Schema({
  communicationId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'COM' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  sender: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recipient: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  type: {
    type: String,
    required: true,
    enum: [
      'Appointment Reminder', 'Treatment Notification', 'Follow-up Alert', 'Lab Results',
      'Prescription Ready', 'Insurance Update', 'General Message', 'Emergency Alert',
      'Educational Material', 'Survey Request', 'Payment Reminder', 'Discharge Instructions'
    ],
    index: true
  },
  method: {
    type: String,
    required: true,
    enum: ['Email', 'SMS', 'Phone Call', 'In-App Notification', 'Portal Message', 'Letter'],
    index: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Urgent'],
    default: 'Medium',
    index: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  templateUsed: {
    type: String,
    trim: true
  },
  scheduledAt: {
    type: Date,
    index: true
  },
  sentAt: {
    type: Date,
    index: true
  },
  deliveredAt: {
    type: Date,
    index: true
  },
  readAt: {
    type: Date,
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Scheduled', 'Sent', 'Delivered', 'Read', 'Failed', 'Cancelled'],
    default: 'Draft',
    index: true
  },
  deliveryStatus: {
    type: String,
    required: true,
    enum: ['Pending', 'Delivered', 'Failed', 'Bounced', 'Undeliverable'],
    default: 'Pending',
    index: true
  },
  responseRequired: {
    type: Boolean,
    default: false
  },
  responseReceived: {
    type: Boolean,
    default: false
  },
  responseDate: Date,
  responseContent: {
    type: String,
    trim: true
  },
  appointment: {
    type: Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  visit: {
    type: Schema.Types.ObjectId,
    ref: 'PatientVisit'
  },
  treatmentPlan: {
    type: Schema.Types.ObjectId,
    ref: 'TreatmentPlan'
  },
  bill: {
    type: Schema.Types.ObjectId,
    ref: 'Bill'
  },
  deliveryDetails: {
    emailAddress: String,
    phoneNumber: String,
    deliveryAttempts: { type: Number, default: 0 },
    lastAttemptAt: Date,
    errorMessage: String,
    providerResponse: String
  },
  personalizedContent: [{
    placeholder: { type: String, required: true },
    value: { type: String, required: true }
  }],
  attachments: [{
    fileName: { type: String, required: true },
    fileUrl: { type: String, required: true },
    fileType: { type: String, required: true },
    fileSize: { type: Number, required: true }
  }],
  consentGiven: {
    type: Boolean,
    required: true,
    default: false
  },
  consentDate: Date,
  optOutRequested: {
    type: Boolean,
    default: false,
    index: true
  },
  optOutDate: Date,
  campaignId: {
    type: String,
    trim: true
  },
  campaignName: {
    type: String,
    trim: true
  },
  analytics: {
    opened: { type: Boolean, default: false },
    openedAt: Date,
    clicked: { type: Boolean, default: false },
    clickedAt: Date,
    clickedLinks: [{
      url: { type: String, required: true },
      clickedAt: { type: Date, required: true }
    }]
  },
  followUpRequired: {
    type: Boolean,
    default: false
  },
  followUpDate: Date,
  followUpCompleted: {
    type: Boolean,
    default: false
  },
  followUpNotes: {
    type: String,
    trim: true
  },
  tags: {
    type: [String],
    index: true
  },
  notes: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
PatientCommunicationSchema.index({ communicationId: 1 });
PatientCommunicationSchema.index({ patient: 1, createdAt: -1 });
PatientCommunicationSchema.index({ patient: 1, type: 1 });
PatientCommunicationSchema.index({ patient: 1, status: 1 });
PatientCommunicationSchema.index({ sender: 1, createdAt: -1 });
PatientCommunicationSchema.index({ scheduledAt: 1, status: 1 });
PatientCommunicationSchema.index({ method: 1, status: 1 });
PatientCommunicationSchema.index({ priority: 1, status: 1 });

// Compound indexes
PatientCommunicationSchema.index({ patient: 1, method: 1, status: 1 });
PatientCommunicationSchema.index({ type: 1, scheduledAt: 1, status: 1 });
PatientCommunicationSchema.index({ campaignId: 1, status: 1 });

// Text index for search
PatientCommunicationSchema.index({
  subject: 'text',
  message: 'text',
  tags: 'text'
});

export default mongoose.model<IPatientCommunication>('PatientCommunication', PatientCommunicationSchema);
