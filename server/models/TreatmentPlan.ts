import mongoose, { Schema, Document } from 'mongoose';

export interface ITreatmentPlan extends Document {
  planId: string;
  patient: mongoose.Types.ObjectId;
  doctor: mongoose.Types.ObjectId;
  visit?: mongoose.Types.ObjectId;
  planName: string;
  planType: 'Acute' | 'Chronic' | 'Preventive' | 'Rehabilitation' | 'Palliative';
  startDate: Date;
  endDate?: Date;
  status: 'Active' | 'Completed' | 'Discontinued' | 'On Hold' | 'Cancelled';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  
  // Primary condition being treated
  primaryCondition: {
    diagnosis: string;
    icdCode?: string;
    severity: 'Mild' | 'Moderate' | 'Severe';
    stage?: string;
  };
  
  // Treatment goals
  goals: Array<{
    goalId: string;
    description: string;
    targetDate?: Date;
    status: 'Not Started' | 'In Progress' | 'Achieved' | 'Modified' | 'Discontinued';
    measurableOutcome?: string;
    progress?: number; // percentage 0-100
    notes?: string;
  }>;
  
  // Medication management
  medications: Array<{
    medicationId: string;
    name: string;
    dosage: string;
    frequency: string;
    route: 'Oral' | 'IV' | 'IM' | 'Topical' | 'Inhaled' | 'Sublingual' | 'Other';
    startDate: Date;
    endDate?: Date;
    duration?: string;
    instructions: string;
    prescribedBy: mongoose.Types.ObjectId;
    status: 'Active' | 'Completed' | 'Discontinued' | 'On Hold';
    sideEffects?: string[];
    adherence?: {
      percentage: number;
      lastAssessed: Date;
      notes?: string;
    };
    refillsRemaining?: number;
    nextRefillDate?: Date;
  }>;
  
  // Therapy and procedures
  therapies: Array<{
    therapyId: string;
    type: 'Physical Therapy' | 'Occupational Therapy' | 'Speech Therapy' | 'Respiratory Therapy' | 'Psychological Therapy' | 'Other';
    description: string;
    frequency: string;
    duration: string;
    startDate: Date;
    endDate?: Date;
    provider?: string;
    location?: string;
    status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled';
    sessions: Array<{
      sessionDate: Date;
      duration: number;
      notes?: string;
      outcome?: string;
      nextSession?: Date;
    }>;
    goals?: string[];
    progress?: string;
  }>;
  
  // Monitoring and follow-up
  monitoring: {
    vitalSigns: {
      frequency: string;
      parameters: string[];
      targetRanges?: Array<{
        parameter: string;
        minValue?: number;
        maxValue?: number;
        unit: string;
      }>;
    };
    labTests: Array<{
      testName: string;
      frequency: string;
      nextDueDate?: Date;
      targetRange?: string;
      criticalValues?: string;
    }>;
    imaging: Array<{
      type: string;
      frequency: string;
      nextDueDate?: Date;
      indication: string;
    }>;
    appointments: Array<{
      type: string;
      frequency: string;
      nextDueDate?: Date;
      department?: string;
      provider?: string;
    }>;
  };
  
  // Lifestyle modifications
  lifestyleModifications: Array<{
    category: 'Diet' | 'Exercise' | 'Sleep' | 'Stress Management' | 'Smoking Cessation' | 'Alcohol Reduction' | 'Other';
    description: string;
    instructions: string;
    targetDate?: Date;
    status: 'Recommended' | 'In Progress' | 'Achieved' | 'Not Followed';
    progress?: string;
  }>;
  
  // Patient education
  patientEducation: Array<{
    topic: string;
    materials: string[];
    dateProvided: Date;
    providedBy: mongoose.Types.ObjectId;
    understood: boolean;
    notes?: string;
  }>;
  
  // Care team
  careTeam: Array<{
    member: mongoose.Types.ObjectId;
    role: string;
    responsibilities: string[];
    contactInfo?: string;
    primaryContact: boolean;
  }>;
  
  // Progress tracking
  progressNotes: Array<{
    date: Date;
    author: mongoose.Types.ObjectId;
    type: 'Assessment' | 'Progress Update' | 'Medication Change' | 'Goal Modification' | 'Other';
    content: string;
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileType: string;
    }>;
  }>;
  
  // Outcomes and metrics
  outcomes: {
    qualityOfLife?: {
      score: number;
      scale: string;
      assessmentDate: Date;
    };
    functionalStatus?: {
      score: number;
      scale: string;
      assessmentDate: Date;
    };
    painLevel?: {
      score: number;
      scale: string;
      assessmentDate: Date;
    };
    customMetrics?: Array<{
      name: string;
      value: string;
      unit?: string;
      assessmentDate: Date;
    }>;
  };
  
  // Emergency information
  emergencyProtocol?: {
    warningSignsSymptoms: string[];
    emergencyContacts: Array<{
      name: string;
      relationship: string;
      phone: string;
    }>;
    emergencyInstructions: string;
    medicationsToAvoid?: string[];
  };
  
  // Plan notes and comments
  planNotes: string;
  privateNotes?: string;
  
  // Review and approval
  reviewedBy?: mongoose.Types.ObjectId;
  reviewedAt?: Date;
  approvedBy?: mongoose.Types.ObjectId;
  approvedAt?: Date;
  
  // Metadata
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  lastModified: Date;
}

const TreatmentPlanSchema: Schema = new Schema({
  planId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'TP' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  visit: {
    type: Schema.Types.ObjectId,
    ref: 'PatientVisit'
  },
  planName: {
    type: String,
    required: true,
    trim: true
  },
  planType: {
    type: String,
    required: true,
    enum: ['Acute', 'Chronic', 'Preventive', 'Rehabilitation', 'Palliative'],
    index: true
  },
  startDate: {
    type: Date,
    required: true,
    index: true
  },
  endDate: {
    type: Date,
    index: true
  },
  status: {
    type: String,
    required: true,
    enum: ['Active', 'Completed', 'Discontinued', 'On Hold', 'Cancelled'],
    default: 'Active',
    index: true
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    default: 'Medium',
    index: true
  },
  primaryCondition: {
    diagnosis: { type: String, required: true },
    icdCode: String,
    severity: {
      type: String,
      enum: ['Mild', 'Moderate', 'Severe'],
      required: true
    },
    stage: String
  },
  goals: [{
    goalId: {
      type: String,
      required: true,
      default: function() {
        return 'GL' + Date.now().toString().slice(-6);
      }
    },
    description: { type: String, required: true },
    targetDate: Date,
    status: {
      type: String,
      enum: ['Not Started', 'In Progress', 'Achieved', 'Modified', 'Discontinued'],
      default: 'Not Started'
    },
    measurableOutcome: String,
    progress: { type: Number, min: 0, max: 100, default: 0 },
    notes: String
  }],
  medications: [{
    medicationId: {
      type: String,
      required: true,
      default: function() {
        return 'MD' + Date.now().toString().slice(-6);
      }
    },
    name: { type: String, required: true },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    route: {
      type: String,
      enum: ['Oral', 'IV', 'IM', 'Topical', 'Inhaled', 'Sublingual', 'Other'],
      default: 'Oral'
    },
    startDate: { type: Date, required: true },
    endDate: Date,
    duration: String,
    instructions: { type: String, required: true },
    prescribedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['Active', 'Completed', 'Discontinued', 'On Hold'],
      default: 'Active'
    },
    sideEffects: [String],
    adherence: {
      percentage: { type: Number, min: 0, max: 100 },
      lastAssessed: Date,
      notes: String
    },
    refillsRemaining: { type: Number, min: 0 },
    nextRefillDate: Date
  }],
  therapies: [{
    therapyId: {
      type: String,
      required: true,
      default: function() {
        return 'TH' + Date.now().toString().slice(-6);
      }
    },
    type: {
      type: String,
      enum: ['Physical Therapy', 'Occupational Therapy', 'Speech Therapy', 'Respiratory Therapy', 'Psychological Therapy', 'Other'],
      required: true
    },
    description: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    startDate: { type: Date, required: true },
    endDate: Date,
    provider: String,
    location: String,
    status: {
      type: String,
      enum: ['Scheduled', 'In Progress', 'Completed', 'Cancelled'],
      default: 'Scheduled'
    },
    sessions: [{
      sessionDate: { type: Date, required: true },
      duration: { type: Number, required: true },
      notes: String,
      outcome: String,
      nextSession: Date
    }],
    goals: [String],
    progress: String
  }],
  monitoring: {
    vitalSigns: {
      frequency: String,
      parameters: [String],
      targetRanges: [{
        parameter: { type: String, required: true },
        minValue: Number,
        maxValue: Number,
        unit: { type: String, required: true }
      }]
    },
    labTests: [{
      testName: { type: String, required: true },
      frequency: { type: String, required: true },
      nextDueDate: Date,
      targetRange: String,
      criticalValues: String
    }],
    imaging: [{
      type: { type: String, required: true },
      frequency: { type: String, required: true },
      nextDueDate: Date,
      indication: { type: String, required: true }
    }],
    appointments: [{
      type: { type: String, required: true },
      frequency: { type: String, required: true },
      nextDueDate: Date,
      department: String,
      provider: String
    }]
  },
  lifestyleModifications: [{
    category: {
      type: String,
      enum: ['Diet', 'Exercise', 'Sleep', 'Stress Management', 'Smoking Cessation', 'Alcohol Reduction', 'Other'],
      required: true
    },
    description: { type: String, required: true },
    instructions: { type: String, required: true },
    targetDate: Date,
    status: {
      type: String,
      enum: ['Recommended', 'In Progress', 'Achieved', 'Not Followed'],
      default: 'Recommended'
    },
    progress: String
  }],
  patientEducation: [{
    topic: { type: String, required: true },
    materials: [String],
    dateProvided: { type: Date, required: true },
    providedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    understood: { type: Boolean, required: true },
    notes: String
  }],
  careTeam: [{
    member: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: { type: String, required: true },
    responsibilities: [String],
    contactInfo: String,
    primaryContact: { type: Boolean, default: false }
  }],
  progressNotes: [{
    date: { type: Date, required: true },
    author: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    type: {
      type: String,
      enum: ['Assessment', 'Progress Update', 'Medication Change', 'Goal Modification', 'Other'],
      required: true
    },
    content: { type: String, required: true },
    attachments: [{
      fileName: { type: String, required: true },
      fileUrl: { type: String, required: true },
      fileType: { type: String, required: true }
    }]
  }],
  outcomes: {
    qualityOfLife: {
      score: Number,
      scale: String,
      assessmentDate: Date
    },
    functionalStatus: {
      score: Number,
      scale: String,
      assessmentDate: Date
    },
    painLevel: {
      score: Number,
      scale: String,
      assessmentDate: Date
    },
    customMetrics: [{
      name: { type: String, required: true },
      value: { type: String, required: true },
      unit: String,
      assessmentDate: { type: Date, required: true }
    }]
  },
  emergencyProtocol: {
    warningSignsSymptoms: [String],
    emergencyContacts: [{
      name: { type: String, required: true },
      relationship: { type: String, required: true },
      phone: { type: String, required: true }
    }],
    emergencyInstructions: String,
    medicationsToAvoid: [String]
  },
  planNotes: {
    type: String,
    required: true,
    trim: true
  },
  privateNotes: {
    type: String,
    trim: true
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModified: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
TreatmentPlanSchema.index({ planId: 1 });
TreatmentPlanSchema.index({ patient: 1, status: 1 });
TreatmentPlanSchema.index({ doctor: 1, status: 1 });
TreatmentPlanSchema.index({ planType: 1, status: 1 });
TreatmentPlanSchema.index({ priority: 1, status: 1 });
TreatmentPlanSchema.index({ startDate: -1 });
TreatmentPlanSchema.index({ endDate: 1 });

// Compound indexes
TreatmentPlanSchema.index({ patient: 1, startDate: -1, status: 1 });
TreatmentPlanSchema.index({ doctor: 1, startDate: -1, status: 1 });

// Pre-save middleware to update lastModified
TreatmentPlanSchema.pre('save', function(next) {
  this.lastModified = new Date();
  next();
});

export default mongoose.model<ITreatmentPlan>('TreatmentPlan', TreatmentPlanSchema);
