import User from '../models/User.js';
import Patient from '../models/Patient.js';
import PatientVisit from '../models/PatientVisit.js';
import Role from '../models/Role.js';
import Permission from '../models/Permission.js';
import AuditLog from '../models/AuditLog.js';
import Appointment from '../models/Appointment.js';
import Notification from '../models/Notification.js';

// Model registry to provide access to all models
const models = {
  User,
  Patient,
  PatientVisit,
  Role,
  Permission,
  AuditLog,
  Appointment,
  Notification
};

// Function to get all models
export const getModels = () => {
  return models;
};

// Function to get a specific model
export const getModel = (modelName) => {
  return models[modelName];
};

// Function to check if a model exists
export const hasModel = (modelName) => {
  return modelName in models;
};

// Function to register a new model
export const registerModel = (modelName, model) => {
  models[modelName] = model;
};

export default getModels;
